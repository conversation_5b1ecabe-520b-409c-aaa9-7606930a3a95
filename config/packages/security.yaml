security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        users_in_memory: { memory: null }

        keycloak:
            id: App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUserProvider
    firewalls:
        local:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        api:
            pattern: '^/user-management-api'
            security: true
            stateless: true
            provider: keycloak
            access_token:
                token_handler:
                    oidc:
                        claim: preferred_username
                        algorithms: ['RS256', 'ES256']
                        audience: 'hermes-iam'
                        discovery:
                            base_uri: '%env(KEYCLOAK_URL)%/realms/%env(KEYCLOAK_REALM)%/'
                            cache:
                                id: keycloak.cache
                        issuers:
                            - '%env(KEYCLOAK_URL)%/realms/%env(KEYCLOAK_REALM)%'
                            - 'http://localhost:11000/realms/%env(KEYCLOAK_REALM)%' # For local development, won't be an issue on prod
                            - 'http://host.docker.internal:11000/realms/%env(KEYCLOAK_REALM)%' # For local development, won't be an issue on prod
        main:
            lazy: true
            provider: users_in_memory

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
         - { path: ^/user-management-api, roles: ROLE_FULL_ACCESS }
