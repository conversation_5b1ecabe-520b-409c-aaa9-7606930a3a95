api:
    consider_nullable_properties_as_optional: true
    areas:
        user-management:
            resource_path: 'src/Infrastructure/UserManagementApi/Resource'
            url_prefix: '/user-management-api/v1'
            global_request_headers:
                Accept-Language:
                    type: 'string'
                    example: 'en'
                    description: 'The language of the response, default is en'
            open_api:
                info:
                    title: User Management API
                    description: Autogenerated documentation for User Management API
                    version: 1.0.0
                components:
                    securitySchemes:
                        Bearer:
                            type: http
                            scheme: bearer
                            bearerFormat: JWT
                security:
                    -   Bearer: [ ]
