# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'

    # Note that the session will be started ONLY if you read or write from it.
    session: false

    #esi: true
    #fragments: true

    trusted_proxies: '**********/16,10.0.0.0/8,***********/16,**********/12,***********/16,0.0.0.0/8,240.0.0.0/4'
    trusted_headers: [ 'x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto', 'x-forwarded-port', 'x-forwarded-prefix' ]

    http_client:
        scoped_clients:
            keycloak.client:
                base_uri: '%env(KEYCLOAK_URL)%'
