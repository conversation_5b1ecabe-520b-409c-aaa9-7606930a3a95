parameters:
    # List per client, which groups are allowed to be managed by that client
    app.client-groups:
        hermes-backend:
            - hermes-driver
            - hermes-portal-admin
            - hermes-portal-country-admin
            - hermes-portal-faq-admin
            - hermes-portal-support-user
            - hermes-portal-user
            - tenant-de
            - tenant-es
            - tenant-lu
            - tenant-nl
        iot-backend:
            - iot-portal-user
        delphi-backend:
            - delphi-portal-user

    # List of groups that represent a tenant, only one of which can be assigned to a user at a time
    app.unique-tenant-groups:
        - tenant-de
        - tenant-es
        - tenant-lu
        - tenant-nl

    # Groups that once assigned to a user, cannot be removed
    app.protected-groups:
        - tenant-de
        - tenant-es
        - tenant-lu
        - tenant-nl
