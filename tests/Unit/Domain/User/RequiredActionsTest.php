<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain\User;

use App\Domain\User\Enum\RequiredAction;
use App\Domain\User\Exception\InvalidRequiredActionException;
use App\Domain\User\RequiredActions;
use PHPUnit\Framework\TestCase;

class RequiredActionsTest extends TestCase
{
    public function testValidateWithValidActions(): void
    {
        $validActions = [
            RequiredAction::UPDATE_PASSWORD,
        ];

        // Should not throw any exception
        RequiredActions::validate($validActions);
        $this->assertTrue(true); // Assert that we reach this point
    }

    public function testValidateWithEmptyArray(): void
    {
        // Should not throw any exception
        RequiredActions::validate([]);
        $this->assertTrue(true); // Assert that we reach this point
    }

    public function testValidateThrowsExceptionForInvalidAction(): void
    {
        $this->expectException(InvalidRequiredActionException::class);
        $this->expectExceptionMessage('Invalid required action given');

        RequiredActions::validate(['INVALID_ACTION']);
    }

    public function testValidateThrowsExceptionForNonStringAction(): void
    {
        $this->expectException(InvalidRequiredActionException::class);
        $this->expectExceptionMessage('Invalid required action given');

        RequiredActions::validate([123]);
    }

    public function testValidateThrowsExceptionForDuplicateActions(): void
    {
        $this->expectException(InvalidRequiredActionException::class);
        $this->expectExceptionMessage('Duplicate required actions are not allowed');

        RequiredActions::validate([
            RequiredAction::UPDATE_PASSWORD,
            RequiredAction::UPDATE_PASSWORD,
        ]);
    }
}
