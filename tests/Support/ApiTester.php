<?php

declare(strict_types=1);

namespace App\Tests\Support;

/**
 * Inherited Methods.
 *
 * @method void wantTo($text)
 * @method void wantToTest($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause($vars = [])
 *
 * @SuppressWarnings(PHPMD)
 */
class ApiTester extends \Codeception\Actor
{
    use _generated\ApiTesterActions;

    private const string HERMES_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY = 'hermes-backend-client-access-token';
    private const string IOT_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY = 'iot-backend-client-access-token';
    private const string DELPHI_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY = 'delphi-backend-client-access-token';

    public function amAuthenticatedAsHermesBackendClient(): void
    {
        if ($this->checkInCache(self::HERMES_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY)) {
            $accessToken = $this->getFromCache(self::HERMES_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY);
        } else {
            $accessToken = $this->authenticateWithKeycloakUsingClientCredentials(
                clientId: $_ENV['KEYCLOAK_HERMES_CLIENT_ID'],
                clientSecret: $_ENV['KEYCLOAK_HERMES_CLIENT_SECRET'],
            );

            assert(null !== $accessToken);

            $this->storeInCache(self::HERMES_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY, $accessToken);
        }

        $this->amBearerAuthenticated($accessToken);
    }

    public function amAuthenticatedAsIotBackendClient(): void
    {
        if ($this->checkInCache(self::IOT_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY)) {
            $accessToken = $this->getFromCache(self::IOT_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY);
        } else {
            $accessToken = $this->authenticateWithKeycloakUsingClientCredentials(
                clientId: $_ENV['KEYCLOAK_IOT_CLIENT_ID'],
                clientSecret: $_ENV['KEYCLOAK_IOT_CLIENT_SECRET'],
            );

            assert(null !== $accessToken);

            $this->storeInCache(self::IOT_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY, $accessToken);
        }

        $this->amBearerAuthenticated($accessToken);
    }

    public function amAuthenticatedAsDelphiBackendClient(): void
    {
        if ($this->checkInCache(self::DELPHI_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY)) {
            $accessToken = $this->getFromCache(self::DELPHI_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY);
        } else {
            $accessToken = $this->authenticateWithKeycloakUsingClientCredentials(
                clientId: $_ENV['KEYCLOAK_DELPHI_CLIENT_ID'],
                clientSecret: $_ENV['KEYCLOAK_DELPHI_CLIENT_SECRET'],
            );

            assert(null !== $accessToken);

            $this->storeInCache(self::DELPHI_BACKEND_CLIENT_ACCESS_TOKEN_CACHE_KEY, $accessToken);
        }

        $this->amBearerAuthenticated($accessToken);
    }

    public function validateResponseAgainstOpenApiSpecification(): void
    {
        $this->validateResponseAgainstOpenApiSpecificationFile(codecept_data_dir('openapi.json'));
    }
}
