<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

use Codeception\Lib\Interfaces\DependsOnModule;
use Codeception\Module\REST;
use GuzzleHttp\Psr7\Response;
use League\OpenAPIValidation\PSR7\Exception\ValidationFailed;
use League\OpenAPIValidation\PSR7\OperationAddress;
use League\OpenAPIValidation\PSR7\ValidatorBuilder;
use Psr\Http\Message\ResponseInterface;

class OpenApiValidation extends \Codeception\Module implements DependsOnModule
{
    private array $validationBuilderCache = [];
    private REST $restModule;

    public function _inject(REST $rest): void
    {
        $this->restModule = $rest;
    }

    public function _depends(): array
    {
        return [
            REST::class => 'REST module is required to validate responses',
        ];
    }

    public function validateResponseAgainstOpenApiSpecificationFile(string $jsonSpecificationPath): void
    {
        $validationBuilder = $this->getValidationBuilder($jsonSpecificationPath);

        try {
            $validationBuilder->getResponseValidator()->validate(
                $this->getCurrentOperationAddress(),
                $this->getPsrResponse()
            );
        } catch (ValidationFailed $e) {
            $this->fail('Response did not match OpenAPI specification: '.$e->getMessage());
        }
    }

    private function getValidationBuilder(string $jsonSpecificationPath): ValidatorBuilder
    {
        if (!isset($this->validationBuilderCache[$jsonSpecificationPath])) {
            $this->validationBuilderCache[$jsonSpecificationPath] = new ValidatorBuilder()->fromJsonFile($jsonSpecificationPath);
        }

        return $this->validationBuilderCache[$jsonSpecificationPath];
    }

    private function getCurrentOperationAddress(): OperationAddress
    {
        $request = $this->restModule->client->getInternalRequest();

        return new OperationAddress(
            path: $request->getUri(),
            method: strtolower($request->getMethod()),
        );
    }

    private function getPsrResponse(): ResponseInterface
    {
        $response = $this->restModule->client->getInternalResponse();

        return new Response(
            status: $response->getStatusCode(),
            headers: $response->getHeaders(),
            body: $response->getContent(),
        );
    }
}
