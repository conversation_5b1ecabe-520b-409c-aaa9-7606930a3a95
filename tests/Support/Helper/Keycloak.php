<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;

class Keycloak extends \Codeception\Module
{
    public function authenticateWithKeycloakUsingClientCredentials(string $clientId, string $clientSecret): ?string
    {
        $httpClient = HttpClient::createForBaseUri($_ENV['KEYCLOAK_URL']);
        $body = [
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'grant_type' => 'client_credentials',
        ];

        $this->debugSection('Keycloak Authentication Request Params', $body);

        try {
            $response = $httpClient->request(
                'POST',
                "/realms/{$_ENV['KEYCLOAK_REALM']}/protocol/openid-connect/token",
                ['body' => $body],
            );

            $responseCode = $response->getStatusCode();
            $responseContent = $response->getContent();

            if (200 !== $responseCode) {
                $this->debugSection('Keycloak Authentication Request', $response->getInfo());

                $this->fail(
                    sprintf(
                        'Failed to authenticate with Keycloak. Response code: %d, Response: %s',
                        $responseCode,
                        $response->getContent(throw: false)
                    )
                );

                return null;
            }
        } catch (ExceptionInterface $e) {
            $this->debugSection('Keycloak Authentication Request', $response->getInfo());

            $this->fail(sprintf(
                'Failed to authenticate with Keycloak. Exception: %s',
                $e->getMessage()
            ));

            return null;
        }

        $this->debugSection('Keycloak Authentication Request', $response->getInfo());
        $this->debugSection('Keycloak Authentication Response', $responseContent);

        try {
            $decodedResponse = json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            $this->fail(sprintf(
                'Failed to decode Keycloak authentication response. Exception: %s',
                $e->getMessage()
            ));

            return null;
        }

        if (!isset($decodedResponse['access_token'])) {
            $this->fail('Failed to authenticate with Keycloak. Access token not found in response');

            return null;
        }

        return $decodedResponse['access_token'];
    }
}
