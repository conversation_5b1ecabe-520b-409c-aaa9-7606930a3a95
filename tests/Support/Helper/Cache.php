<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

class Cache extends \Codeception\Module
{
    private array $cache = [];

    public function storeInCache(string $key, mixed $value): void
    {
        $this->cache[$key] = $value;
    }

    public function getFromCache(string $key): mixed
    {
        return $this->cache[$key] ?? null;
    }

    public function clearCache(): void
    {
        $this->cache = [];
    }

    public function checkInCache(string $key): bool
    {
        return isset($this->cache[$key]);
    }
}
