{"openapi": "3.1.0", "info": {"title": "User Management API", "version": "1.0.0", "description": "Autogenerated documentation for User Management API"}, "paths": {"/user-management-api/v1/user/check-username/{username}": {"get": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_get_0", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}, {"name": "username", "in": "path", "description": "The username to check", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "pattern": "[a-zA-Z0-9_\\-\\.@+]{1,250}"}}], "responses": {"200": {"description": "Success, no response content"}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}}}}, "/user-management-api/v1/user/check-email/{email}": {"get": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_get_1", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}, {"name": "email", "in": "path", "description": "The email to check", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "pattern": "[a-zA-Z0-9._+-]{1,150}@[a-zA-Z0-9.-]{1,70}(\\.[a-zA-Z]{2,20})?"}}], "responses": {"200": {"description": "Success, no response content"}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}}}}, "/user-management-api/v1/user/{username}": {"get": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_get_2", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}, {"name": "username", "in": "path", "description": "The username of the user to retrieve", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "pattern": "[a-zA-Z0-9_\\-\\.@+]{1,250}"}}], "responses": {"200": {"description": "Data for single User", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}}}}, "/user-management-api/v1/users": {"post": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_post_3", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}, "description": "", "required": true}, "responses": {"201": {"description": "Resource created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}, "409": {"description": "User already exists"}}}}, "/user-management-api/v1/users/{username}": {"put": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_put_4", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}, {"name": "username", "in": "path", "description": "The username of the user to update", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "pattern": "[a-zA-Z0-9_\\-\\.@+]{1,250}"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "description": "", "required": true}, "responses": {"200": {"description": "Resource updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}}}}, "/user-management-api/v1/users/{username}/password": {"put": {"tags": ["User Management"], "summary": "", "description": "", "operationId": "user-management_User_put_5", "parameters": [{"name": "Accept-Language", "in": "header", "description": "The language of the response, default is en", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}, "example": "en"}, {"name": "username", "in": "path", "description": "The username of the user whose password to update", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "pattern": "[a-zA-Z0-9_\\-\\.@+]{1,250}"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPasswordUpdateRequest"}}}, "description": "", "required": true}, "responses": {"200": {"description": "Success, no response content"}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Access to resource denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "422": {"description": "Request validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiValidationError"}}}}}}}}, "components": {"schemas": {"ApiErrorResponse": {"type": "object", "properties": {"type": {"type": "string"}, "title": {"type": "string"}, "detail": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}]}, "translation": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}]}}, "required": ["type", "title"]}, "UserResponse": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "realmRoles": {"type": "array", "items": {"type": "string"}}, "clientRoles": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "groups": {"type": "array", "items": {"type": "string"}}}, "required": ["username", "email", "firstName", "lastName", "realmRoles", "clientRoles", "groups"]}, "UserCreateRequest": {"type": "object", "properties": {"username": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "groups": {"type": "array", "default": [], "items": {"type": "string"}}, "requiredActions": {"type": "array", "description": "Required actions that the user must complete on first login.", "default": [], "items": {"$ref": "#/components/schemas/RequiredAction"}}}, "required": ["username", "firstname", "lastname", "email", "password"]}, "UserUpdateRequest": {"type": "object", "properties": {"firstname": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}], "maxLength": 250}, "lastname": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}], "maxLength": 250}, "email": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}]}, "groups": {"default": null, "anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}]}}}, "UserPasswordUpdateRequest": {"type": "object", "properties": {"password": {"type": "string"}}, "required": ["password"]}, "ApiValidationError": {"type": "object", "properties": {"type": {"type": "string"}, "title": {"type": "string"}, "detail": {"default": null, "anyOf": [{"type": "string"}, {"type": "null"}]}, "violations": {"default": null, "anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}, {"type": "null"}]}}, "required": ["type", "title"]}, "RequiredAction": {"type": "string", "enum": ["UPDATE_PASSWORD"]}, "ValidationError": {"type": "object", "properties": {"field": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "message": {"type": "string"}}, "required": ["message"]}}, "securitySchemes": {"Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}