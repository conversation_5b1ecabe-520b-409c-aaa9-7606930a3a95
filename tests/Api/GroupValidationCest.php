<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class GroupValidationCest
{
    public function _before(ApiTester $I): void
    {
        // No setup needed - each test will generate its own unique username
    }

    public function cannotCreateUserWithMultipleTenantGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-multiple-tenant-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['tenant-de', 'tenant-es'], // Multiple tenant groups - should fail
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(400);
        $I->validateResponseAgainstOpenApiSpecification();
        $I->seeResponseContainsJson([
            'detail' => 'Only one tenant group can be assigned to a user at a time. Found: tenant-de, tenant-es',
        ]);
    }

    public function canCreateUserWithSingleTenantGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-single-tenant-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'], // Single tenant group - should succeed
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ]);
    }

    public function cannotUpdateUserToHaveMultipleTenantGroups(ApiTester $I): void
    {
        // First create a user with one tenant group
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-update-multiple-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Now try to update to have multiple tenant groups
        $updateData = [
            'groups' => ['hermes-portal-user', 'tenant-de', 'tenant-es'], // Multiple tenant groups
        ];

        $I->sendPut('/user-management-api/v1/users/'.$testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(400);
        $I->validateResponseAgainstOpenApiSpecification();
        $I->seeResponseContainsJson([
            'detail' => 'Only one tenant group can be assigned to a user at a time. Found: tenant-de, tenant-es',
        ]);
    }

    public function cannotRemoveProtectedGroupFromUser(ApiTester $I): void
    {
        // First create a user with a protected group (tenant group)
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-remove-protected-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'], // tenant-de is protected
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Now try to remove the protected group
        $updateData = [
            'groups' => ['hermes-portal-user'], // Removing tenant-de (protected group)
        ];

        $I->sendPut('/user-management-api/v1/users/'.$testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(400);
        $I->validateResponseAgainstOpenApiSpecification();
        $I->seeResponseContainsJson([
            'detail' => 'Protected groups cannot be removed once assigned: tenant-de',
        ]);
    }

    public function canUpdateUserWithSameProtectedGroup(ApiTester $I): void
    {
        // First create a user with a protected group
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-keep-protected-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Update user while keeping the protected group
        $updateData = [
            'firstname' => 'Updated',
            'groups' => ['hermes-portal-admin', 'tenant-de'], // Keeping tenant-de, changing other groups
        ];

        $I->sendPut('/user-management-api/v1/users/'.$testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $testUsername,
            'firstName' => 'Updated',
            'groups' => ['hermes-portal-admin', 'tenant-de'],
        ]);
    }

    public function clientsWithoutTenantGroupsCanCreateUsersNormally(ApiTester $I): void
    {
        // Test that IoT backend (which doesn't manage tenant groups) can create users normally
        $I->amAuthenticatedAsIotBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-iot-no-tenant-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['iot-portal-user'], // No tenant groups
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'groups' => ['iot-portal-user'],
        ]);
    }

    public function clientsWithoutTenantGroupsCannotAssignTenantGroups(ApiTester $I): void
    {
        // Test that IoT backend cannot assign tenant groups (they should be filtered out)
        $I->amAuthenticatedAsIotBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-iot-no-tenant-assign-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['iot-portal-user', 'tenant-de'], // tenant-de should be filtered out
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should only contain the authorized group, tenant-de should be filtered out
        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'groups' => ['iot-portal-user'],
        ]);
    }

    public function clientsWithoutTenantGroupsCanUpdateUsersWithExistingTenantGroups(ApiTester $I): void
    {
        // First create a user with tenant groups using hermes-backend
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $testUsername = 'test-iot-update-existing-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $testUsername,
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => $testUsername.'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Now update with IoT backend - should preserve tenant groups
        $I->amAuthenticatedAsIotBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated',
            'groups' => ['iot-portal-user'], // IoT groups only
        ];

        $I->sendPut('/user-management-api/v1/users/'.$testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should preserve hermes groups and tenant groups, and add iot groups
        $I->seeResponseContainsJson([
            'username' => $testUsername,
            'firstName' => 'Updated',
        ]);

        // Check that all groups are preserved/added correctly
        $I->seeResponseContainsJson(['groups' => ['hermes-portal-user']]);
        $I->seeResponseContainsJson(['groups' => ['tenant-de']]);
        $I->seeResponseContainsJson(['groups' => ['iot-portal-user']]);
    }
}
