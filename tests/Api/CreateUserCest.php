<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class CreateUserCest
{
    public function canCreateUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
        ]);
    }

    public function cannotCreateUserWithoutAuthentication(ApiTester $I): void
    {
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-'.time().'@example.com',
            'password' => 'testpassword123',
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotCreateUserWithInvalidData(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => '', // Invalid: empty username
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'invalid-email', // Invalid: not a valid email
            'password' => '123', // Invalid: too short
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(422);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotCreateDuplicateUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'portal-user-de', // This user already exists in test data
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => '<EMAIL>',
            'password' => 'testpassword123',
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(409); // Conflict
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
