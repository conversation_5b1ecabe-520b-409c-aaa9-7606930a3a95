<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class GetUserCest
{
    public function canGetUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/portal-user-de');
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canGetUserWithoutAuthentication(ApiTester $I): void
    {
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/portal-user-de');
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canGetNonExistingUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/non-existing-user');
        $I->seeResponseCodeIs(404);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canGetUserWithInvalidUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/!@#$%^&%%$%#');
        $I->seeResponseCodeIs(404); // No route found
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
