<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UpdateUserCest
{
    private string $testUsername;

    public function _before(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $this->testUsername = 'update-test-user-'.time().'-'.rand(1000, 9999);

        $userData = [
            'username' => $this->testUsername,
            'firstname' => 'Update',
            'lastname' => 'Test',
            'email' => 'update-test-'.time().'-'.rand(1000, 9999).'@example.com',
            'password' => 'testpassword123',
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canUpdateUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated',
            'lastname' => 'Name',
            'email' => 'updated-'.time().'@example.com',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'firstName' => $updateData['firstname'],
            'lastName' => $updateData['lastname'],
            'email' => $updateData['email'],
        ]);
    }

    public function canUpdatePartialUserData(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        // Only update the firstname
        $updateData = [
            'firstname' => 'PartiallyUpdated',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'firstName' => 'PartiallyUpdated',
        ]);
    }

    public function cannotUpdateNonExistingUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated',
            'lastname' => 'Name',
        ];

        $I->sendPut('/user-management-api/v1/users/non-existing-user-'.time(), json_encode($updateData));
        $I->seeResponseCodeIs(404);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdateUserWithoutAuthentication(ApiTester $I): void
    {
        $I->unsetHttpHeader('Authorization');
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated',
            'lastname' => 'Name',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdateUserWithInvalidData(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'email' => 'invalid-email', // Not a valid email format
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(422);
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
