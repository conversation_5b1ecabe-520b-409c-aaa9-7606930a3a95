<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class CreateUserWithGroupsCest
{
    public function canCreateUserWithAuthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-groups-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-groups-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-user', 'tenant-de'], // These are allowed for hermes-backend
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ]);
    }

    public function canCreateUserWithMixedAuthorizedUnauthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-mixed-groups-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-mixed-groups-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [
                'hermes-portal-user',  // Allowed for hermes-backend
                'tenant-de',           // Allowed for hermes-backend
                'unauthorized-group',  // Not allowed for hermes-backend
            ],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should only contain authorized groups
        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ]);

        // Should not contain unauthorized group
        $I->dontSeeResponseContainsJson([
            'groups' => ['unauthorized-group'],
        ]);
    }

    public function canCreateUserWithOnlyUnauthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-unauthorized-groups-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-unauthorized-groups-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => ['unauthorized-group-1', 'unauthorized-group-2'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should create user with no groups
        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
            'groups' => [],
        ]);
    }

    public function iotBackendClientCanOnlyCreateUsersWithIotGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsIotBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-iot-user-'.time(),
            'firstname' => 'IoT',
            'lastname' => 'User',
            'email' => 'test-iot-user-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [
                'iot-portal-user',    // Allowed for iot-backend
                'tenant-de',          // Not allowed for iot-backend (tenant groups removed)
                'hermes-portal-user', // Not allowed for iot-backend (hermes-backend only)
            ],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should only contain iot-backend authorized groups (tenant groups are filtered out)
        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
            'groups' => ['iot-portal-user'],
        ]);

        // Should not contain hermes-specific groups
        $I->dontSeeResponseContainsJson([
            'groups' => ['hermes-portal-user'],
        ]);
    }

    public function delphiBackendClientCanOnlyCreateUsersWithDelphiGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsDelphiBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-delphi-user-'.time(),
            'firstname' => 'Delphi',
            'lastname' => 'User',
            'email' => 'test-delphi-user-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [
                'tenant-es',          // Not allowed for delphi-backend (tenant groups removed)
                'delphi-portal-user', // Allowed for delphi-backend
                'hermes-portal-user', // Not allowed for delphi-backend (hermes-backend only)
            ],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should only contain delphi-backend authorized groups (tenant groups are filtered out)
        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
            'groups' => ['delphi-portal-user'],
        ]);

        // Should not contain hermes-specific groups
        $I->dontSeeResponseContainsJson([
            'groups' => ['hermes-portal-user'],
        ]);
    }
}
