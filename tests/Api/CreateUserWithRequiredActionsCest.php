<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class CreateUserWithRequiredActionsCest
{
    public function canCreateUserWithUpdatePasswordRequiredAction(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-update-password-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-update-password-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
            'requiredActions' => ['UPDATE_PASSWORD'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
        ]);
    }

    public function canCreateUserWithoutRequiredActions(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-no-actions-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-no-actions-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
            'requiredActions' => [],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $userData['username'],
            'firstName' => $userData['firstname'],
            'lastName' => $userData['lastname'],
            'email' => $userData['email'],
        ]);
    }

    public function cannotCreateUserWithInvalidRequiredAction(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-invalid-action-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-invalid-action-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
            'requiredActions' => ['INVALID_ACTION'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(400); // Domain validation error
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotCreateUserWithDuplicateRequiredActions(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-duplicate-actions-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-duplicate-actions-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
            'requiredActions' => ['UPDATE_PASSWORD', 'UPDATE_PASSWORD'],
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(400); // BadRequestHttpException from domain validation
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotCreateUserWithNonStringRequiredAction(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $userData = [
            'username' => 'test-user-non-string-action-'.time(),
            'firstname' => 'Test',
            'lastname' => 'User',
            'email' => 'test-user-non-string-action-'.time().'@example.com',
            'password' => 'testpassword123',
            'groups' => [],
            'requiredActions' => [123], // Non-string value
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(400); // Serialization error
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
