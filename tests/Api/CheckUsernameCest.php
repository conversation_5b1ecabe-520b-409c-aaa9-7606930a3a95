<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class CheckUsernameCest
{
    public function checkExistingUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-username/portal-user-de');
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkNonExistingUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-username/non-existing-username');
        $I->seeResponseCodeIs(404);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkInvalidUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-username/!@#$%^&%%$%#');
        $I->seeResponseCodeIs(404); // No route found
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkUsernameWithoutAuthentication(ApiTester $I): void
    {
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-username/portal-user-de');
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
