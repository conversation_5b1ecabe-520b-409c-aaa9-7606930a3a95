<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class CheckEmailCest
{
    public function checkExistingEmail(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-email/portal-user-de@prezero');
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkNonExistingEmail(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-email/non-existing-email@prezero');
        $I->seeResponseCodeIs(404);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkInvalidEmail(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-email/!@#$%^&%%$%#');
        $I->seeResponseCodeIs(404); // No route found
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function checkEmailWithoutAuthentication(ApiTester $I): void
    {
        $I->haveHttpHeader('Accept', 'application/json');
        $I->sendGet('/user-management-api/v1/user/check-email/portal-user-de@prezero');
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
