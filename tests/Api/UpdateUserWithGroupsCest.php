<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UpdateUserWithGroupsCest
{
    private string $testUsername;

    public function _before(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $this->testUsername = 'update-groups-test-user-'.time().'-'.rand(1000, 9999);

        // First create user with hermes-backend client (authorized groups)
        $userData = [
            'username' => $this->testUsername,
            'firstname' => 'Update',
            'lastname' => 'Test',
            'email' => 'update-groups-test-'.time().'-'.rand(1000, 9999).'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-driver', 'tenant-de'], // Authorized for hermes-backend
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canUpdateUserWithAuthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated',
            'lastname' => 'Name',
            'groups' => ['hermes-portal-admin', 'tenant-de'], // Keep the same tenant group (protected)
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'firstName' => $updateData['firstname'],
            'lastName' => $updateData['lastname'],
            'groups' => ['hermes-portal-admin', 'tenant-de'],
        ]);
    }

    public function canUpdateUserWithMixedAuthorizedUnauthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'groups' => [
                'hermes-portal-user', // Authorized for hermes-backend
                'tenant-de',          // Keep the same tenant group (protected)
                'unauthorized-group', // Not authorized for hermes-backend
                'iot-portal-user', // Not authorized for hermes-backend
            ],
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should only contain authorized groups from the request
        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'groups' => ['hermes-portal-user', 'tenant-de'],
        ]);

        // Should not contain unauthorized group
        $I->dontSeeResponseContains('unauthorized-group');
        $I->dontSeeResponseContains('iot-portal-user');
    }

    public function iotBackendClientCanOnlyUpdateIotAuthorizedGroups(ApiTester $I): void
    {
        // First, create a user with hermes-backend client
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $iotTestUsername = 'iot-update-test-user-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $iotTestUsername,
            'firstname' => 'IoT',
            'lastname' => 'Test',
            'email' => 'iot-update-test-'.time().'-'.rand(1000, 9999).'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-driver', 'tenant-de'], // Hermes groups
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Now try to update with IoT backend client
        $I->amAuthenticatedAsIotBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'groups' => [
                'tenant-de',         // Keep the same tenant group (protected)
                'iot-portal-user',   // Add iot-specific group
                'hermes-driver',     // Not authorized for iot-backend (should be preserved)
            ],
        ];

        $I->sendPut('/user-management-api/v1/users/'.$iotTestUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should contain both preserved hermes groups and iot groups
        $I->seeResponseContainsJson(['groups' => ['hermes-driver']]);
        $I->seeResponseContainsJson(['groups' => ['tenant-de']]);
        $I->seeResponseContainsJson(['groups' => ['iot-portal-user']]);

        // Should contain the protected tenant group
        // The protected tenant group should still be there
    }

    public function canUpdateUserWithOnlyUnauthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'groups' => ['unauthorized-group-1', 'delphi-portal-user', 'tenant-de'],
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should keep the protected tenant group even though no other authorized groups were requested
        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'groups' => ['tenant-de'],
        ]);
    }

    public function canUpdateUserWithoutGroupsParameter(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'firstname' => 'Updated Without Groups',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should preserve existing groups when groups parameter is not provided
        $I->seeResponseContainsJson([
            'username' => $this->testUsername,
            'firstName' => 'Updated Without Groups',
        ]);

        // Verify groups are preserved (should contain the original groups)
        $I->seeResponseContainsJson(['groups' => ['hermes-driver']]);
        $I->seeResponseContainsJson(['groups' => ['tenant-de']]);
    }

    public function delphiBackendClientCanOnlyUpdateDelphiAuthorizedGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        // Create a user with mixed groups using hermes-backend
        $delphiTestUsername = 'delphi-update-test-user-'.time().'-'.rand(1000, 9999);
        $userData = [
            'username' => $delphiTestUsername,
            'firstname' => 'Delphi',
            'lastname' => 'Test',
            'email' => 'delphi-update-test-'.time().'-'.rand(1000, 9999).'@example.com',
            'password' => 'testpassword123',
            'groups' => ['hermes-portal-admin', 'tenant-de'], // Hermes groups
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();

        // Now try to update with Delphi backend client
        $I->amAuthenticatedAsDelphiBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $updateData = [
            'groups' => [
                'tenant-de',             // Keep the protected tenant group
                'delphi-portal-user',    // Authorized for delphi-backend
                'hermes-portal-admin',   // Not authorized for delphi-backend (should be preserved)
            ],
        ];

        $I->sendPut('/user-management-api/v1/users/'.$delphiTestUsername, json_encode($updateData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();

        // Should preserve hermes-portal-admin (outside Delphi scope), keep tenant-de (protected), and add delphi-portal-user
        $I->seeResponseContainsJson(['groups' => ['hermes-portal-admin']]);
        $I->seeResponseContainsJson(['groups' => ['tenant-de']]);
        $I->seeResponseContainsJson(['groups' => ['delphi-portal-user']]);
    }
}
