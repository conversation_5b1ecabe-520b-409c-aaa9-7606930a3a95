<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

class UpdateUserPasswordCest
{
    private string $testUsername;

    public function _before(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $this->testUsername = 'password-test-user-'.time().'-'.rand(1000, 9999);

        $userData = [
            'username' => $this->testUsername,
            'firstname' => 'Password',
            'lastname' => 'Test',
            'email' => 'password-test-'.time().'-'.rand(1000, 9999).'@example.com',
            'password' => 'initialpassword123',
        ];

        $I->sendPost('/user-management-api/v1/users', json_encode($userData));
        $I->seeResponseCodeIs(201);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function canUpdateUserPassword(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [
            'password' => 'newpassword456',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername.'/password', json_encode($passwordData));
        $I->seeResponseCodeIs(200);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdatePasswordWithEmptyPassword(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [
            'password' => '',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername.'/password', json_encode($passwordData));
        $I->seeResponseCodeIs(422);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdatePasswordWithMissingPassword(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [];

        $I->sendPut('/user-management-api/v1/users/'.$this->testUsername.'/password', json_encode($passwordData));
        $I->seeResponseCodeIs(422);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdatePasswordForNonExistentUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [
            'password' => 'newpassword456',
        ];

        $I->sendPut('/user-management-api/v1/users/non-existent-user/password', json_encode($passwordData));
        $I->seeResponseCodeIs(404);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdatePasswordWithoutAuthentication(ApiTester $I): void
    {
        // Create a separate test user for this test to avoid authentication from _before
        $testUsername = 'no-auth-test-user-'.time().'-'.rand(1000, 9999);

        // Clear any existing authentication headers
        $I->unsetHttpHeader('Authorization');
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [
            'password' => 'newpassword456',
        ];

        $I->sendPut('/user-management-api/v1/users/'.$testUsername.'/password', json_encode($passwordData));
        $I->seeResponseCodeIs(401);
        $I->validateResponseAgainstOpenApiSpecification();
    }

    public function cannotUpdatePasswordWithInvalidUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsHermesBackendClient();
        $I->haveHttpHeader('Accept', 'application/json');
        $I->haveHttpHeader('Content-Type', 'application/json');

        $passwordData = [
            'password' => 'newpassword456',
        ];

        $I->sendPut('/user-management-api/v1/users/invalid@username!/password', json_encode($passwordData));
        $I->seeResponseCodeIs(404); // No route found due to regex constraint
        $I->validateResponseAgainstOpenApiSpecification();
    }
}
