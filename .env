# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

APP_ENV=local
APP_DEBUG=1
APP_SECRET=d1d90a331a3178a450e968423915ccec
IAM_URL=http://caddy:8000
KEYCLOAK_URL=http://keycloak:8080
KEYCLOAK_REALM=hermes
<PERSON>K_CLIENT_ID=hermes-iam
<PERSON>_<PERSON>LIENT_SECRET=jjXb58cwVSfD0HvRFRuWdBgUX1FtYU3w
KEYCLOAK_HERMES_CLIENT_ID=hermes-backend
KEYCLOAK_HERMES_CLIENT_SECRET=BojmiWo5Vgiu5Ql59X4LMwJbnKimdnTO
KEYCLOAK_IOT_CLIENT_ID=iot-backend
KEYCLOAK_IOT_CLIENT_SECRET=Ex2o8WZxWxO903ZOAxJrzxkUIBfhdRIH
KEYCLOAK_DELPHI_CLIENT_ID=delphi-backend
KEYCLOAK_DELPHI_CLIENT_SECRET=lcDN4yYjOWJMlDtSQ4e6OkXKhn4NFXOp
STDOUT_LOG_LEVEL=debug
