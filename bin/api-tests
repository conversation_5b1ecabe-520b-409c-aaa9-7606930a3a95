#!/usr/bin/env bash

# Fail on first error
set -eu

if [[ "$APP_ENV" != "dev" && "$APP_ENV" != "local" ]]; then
    echo "This script is only meant to be run in dev or local environments."
    exit;
fi

# Get current script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
INPUT_PARAMS="$*"

# Generate openapi definitions for the tests
echo "Generating User Management API OpenAPI definitions for the tests..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console prezero:api:dump-documentation --format user-management "$PROJECT_DIR"/tests/Support/Data/openapi.json

# Run the tests
rm -rf "$PROJECT_DIR"/tests/_output/*
echo "Running the tests..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/vendor/bin/codecept run $INPUT_PARAMS
