{"enabled": true, "realm": "hermes", "loginTheme": "prezero", "ssoSessionIdleTimeout": 50400, "ssoSessionMaxLifespan": 50400, "accessTokenLifespan": 50400, "eventsEnabled": true, "eventsExpiration": 3600, "adminEventsEnabled": true, "adminEventsDetailsEnabled": true, "requiredActions": [{"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": false, "defaultAction": false, "priority": 90, "config": {}}], "clients": [{"clientId": "hermes-portal", "name": "<PERSON><PERSON>", "enabled": true, "publicClient": true, "directAccessGrantsEnabled": true, "protocolMappers": [{"name": "Tenant", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "tenant", "id.token.claim": "true", "lightweight.claim": "false", "claim.name": "tenant", "jsonType.label": "String", "access.token.claim": "true"}}], "defaultClientScopes": ["profile", "roles", "groups", "email"], "optionalClientScopes": ["hermes-portal"]}, {"clientId": "hermes-app", "name": "<PERSON><PERSON>", "enabled": true, "publicClient": true, "directAccessGrantsEnabled": true, "protocolMappers": [{"name": "Tenant", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "tenant", "id.token.claim": "true", "lightweight.claim": "false", "claim.name": "tenant", "jsonType.label": "String", "access.token.claim": "true"}}], "defaultClientScopes": ["profile", "roles", "groups", "email"], "optionalClientScopes": ["hermes-driver"]}, {"clientId": "hermes-backend", "name": "<PERSON><PERSON>", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "BojmiWo5Vgiu5Ql59X4LMwJbnKimdnTO", "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "1800"}, "defaultClientScopes": ["service_account", "roles", "profile", "basic"], "optionalClientScopes": []}, {"clientId": "hermes-iam", "name": "Hermes IAM API", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "jjXb58cwVSfD0HvRFRuWdBgUX1FtYU3w", "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "1800"}, "defaultClientScopes": ["service_account", "roles", "profile", "basic"], "optionalClientScopes": []}, {"clientId": "iot-backend", "name": "IOT Backend", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "Ex2o8WZxWxO903ZOAxJrzxkUIBfhdRIH", "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "1800"}, "defaultClientScopes": ["service_account", "roles", "profile", "basic"], "optionalClientScopes": []}, {"clientId": "delphi-backend", "name": "Delphi Backend", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "lcDN4yYjOWJMlDtSQ4e6OkXKhn4NFXOp", "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "1800"}, "defaultClientScopes": ["service_account", "roles", "profile", "basic"], "optionalClientScopes": []}], "clientScopes": [{"name": "hermes-driver", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "Audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "config": {"included.client.audience": "hermes-backend", "id.token.claim": "false", "lightweight.claim": "false", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"name": "hermes-portal", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "Audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "config": {"included.client.audience": "hermes-backend", "id.token.claim": "false", "lightweight.claim": "false", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"name": "groups", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "Groups", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "config": {"full.path": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}], "roles": {"realm": [{"name": "hermes-portal-device-message-access", "description": "Access to view device messages and create new ones or respond to existing messages."}, {"name": "hermes-portal-view-country-data", "description": "View country-related data."}, {"name": "hermes-portal-view-equipment", "description": "Access to equipment-informnation"}, {"name": "hermes-portal-view-phonebooks", "description": "view phonebooks"}, {"name": "hermes-portal-view-tours", "description": "Access to tours"}, {"name": "hermes-portal", "description": "legacy"}, {"name": "hermes-portal-manage-faq", "description": "Access to FAQ-management."}, {"name": "hermes-portal-view-trackdata", "description": "Access to trackdata"}, {"name": "hermes-portal-view-mastertour-progress", "description": "Access  to mastertour-progress"}, {"name": "hermes-portal-manage-faq-admins", "description": "Access to faq-admin-management."}, {"name": "hermes-portal-manage-poi", "description": "Access to point-of-interest-management"}, {"name": "hermes-portal-view-files", "description": "Access to hermes-files."}, {"name": "hermes-portal-mobile-app-releases", "description": "Access to view and download mobile app releases."}, {"name": "hermes-portal-manage-support-users", "description": "Access to support-user-management."}, {"name": "hermes-driver", "description": "Access to app API."}, {"name": "hermes-portal-manage-mastertours", "description": "Access to mastertour-management"}, {"name": "hermes-portal-view-branch-data", "description": "View branch-related data."}, {"name": "uma_authorization", "description": "${role_uma_authorization}"}, {"name": "hermes-portal-manage-portal-users", "description": "Access to portal-user-management"}, {"name": "hermes-portal-view-staff", "description": "Access to staff-information."}, {"name": "hermes-portal-view-equipment-checks", "description": "Access to read equipment-checks"}, {"name": "hermes-portal-manage-device-access", "description": "Access to device-access-management."}, {"name": "hermes-portal-manage-dako-accounts", "description": "manage dako-accounts of branches"}, {"name": "hermes-portal-manage-country-admins", "description": "Access to country-admin-management."}, {"name": "hermes-portal-view-dashboard", "description": "Access to dashboard."}, {"name": "hermes-portal-access", "description": "Main access to the portal."}, {"name": "offline_access", "description": "${role_offline-access}"}, {"name": "hermes-portal-manage-phonebooks", "description": "manage phonebooks"}, {"name": "hermes-portal-change-profile", "description": "Access to profile-changes."}, {"name": "iot-portal-access", "description": "Access to IOT portal."}, {"name": "delphi-portal-access", "description": "Access to Delphi portal."}], "client": {"hermes-iam": [{"name": "full-access", "description": "Full access to User Management API"}]}}, "groups": [{"name": "hermes-driver", "path": "/hermes-driver", "realmRoles": ["hermes-driver"]}, {"name": "hermes-portal-admin", "path": "/hermes-portal-admin", "realmRoles": ["hermes-portal-device-message-access", "hermes-portal-manage-portal-users", "hermes-portal-view-country-data", "hermes-portal-view-equipment", "hermes-portal-view-staff", "hermes-portal-view-phonebooks", "hermes-portal-view-tours", "hermes-portal-view-equipment-checks", "hermes-portal-manage-device-access", "hermes-portal-manage-dako-accounts", "hermes-portal-manage-faq", "hermes-portal-view-trackdata", "hermes-portal-manage-country-admins", "hermes-portal-view-mastertour-progress", "hermes-portal-view-dashboard", "hermes-portal-access", "hermes-portal-manage-faq-admins", "hermes-portal-manage-poi", "hermes-portal-view-files", "hermes-portal-mobile-app-releases", "hermes-portal-manage-support-users", "hermes-portal-manage-mastertours", "hermes-portal-manage-phonebooks", "hermes-portal-change-profile"]}, {"name": "hermes-portal-country-admin", "path": "/hermes-portal-country-admin", "realmRoles": ["hermes-portal-device-message-access", "hermes-portal-manage-portal-users", "hermes-portal-view-country-data", "hermes-portal-view-equipment", "hermes-portal-view-staff", "hermes-portal-view-phonebooks", "hermes-portal-view-tours", "hermes-portal-view-equipment-checks", "hermes-portal-manage-device-access", "hermes-portal-manage-dako-accounts", "hermes-portal-manage-faq", "hermes-portal-view-trackdata", "hermes-portal-view-mastertour-progress", "hermes-portal-view-dashboard", "hermes-portal-access", "hermes-portal-manage-poi", "hermes-portal-view-files", "hermes-portal-manage-support-users", "hermes-portal-manage-mastertours", "hermes-portal-manage-phonebooks", "hermes-portal-change-profile"]}, {"name": "hermes-portal-faq-admin", "path": "/hermes-portal-faq-admin", "realmRoles": ["hermes-portal-access", "hermes-portal-manage-faq", "hermes-portal-change-profile"]}, {"name": "hermes-portal-support-user", "path": "/hermes-portal-support-user", "realmRoles": ["hermes-portal-device-message-access", "hermes-portal-view-equipment", "hermes-portal-view-staff", "hermes-portal-view-phonebooks", "hermes-portal-view-tours", "hermes-portal-view-equipment-checks", "hermes-portal-manage-device-access", "hermes-portal-view-trackdata", "hermes-portal-view-mastertour-progress", "hermes-portal-view-dashboard", "hermes-portal-access", "hermes-portal-manage-poi", "hermes-portal-view-files", "hermes-portal-manage-mastertours", "hermes-portal-manage-phonebooks", "hermes-portal-view-branch-data", "hermes-portal-change-profile"]}, {"name": "hermes-portal-user", "path": "/hermes-portal-user", "realmRoles": ["hermes-portal-device-message-access", "hermes-portal-view-equipment", "hermes-portal-view-staff", "hermes-portal-view-phonebooks", "hermes-portal-view-tours", "hermes-portal-view-equipment-checks", "hermes-portal-view-trackdata", "hermes-portal-view-mastertour-progress", "hermes-portal-view-dashboard", "hermes-portal-access", "hermes-portal-manage-poi", "hermes-portal-view-files", "hermes-portal-manage-mastertours", "hermes-portal-manage-phonebooks", "hermes-portal-view-branch-data", "hermes-portal-change-profile"]}, {"name": "tenant-de", "path": "/tenant-de", "attributes": {"tenant": ["pz"]}}, {"name": "tenant-es", "path": "/tenant-es", "attributes": {"tenant": ["pze"]}}, {"name": "tenant-lu", "path": "/tenant-lu", "attributes": {"tenant": ["pzl"]}}, {"name": "tenant-nl", "path": "/tenant-nl", "attributes": {"tenant": ["pzn"]}}, {"name": "iot-portal-user", "path": "/iot-portal-user", "realmRoles": ["iot-portal-access"]}, {"name": "delphi-portal-user", "path": "/delphi-portal-user", "realmRoles": ["delphi-portal-access"]}], "users": [{"username": "hermes-backend", "email": "hermes-backend@prezero", "enabled": true, "firstName": "<PERSON><PERSON>", "lastName": "Backend", "emailVerified": true, "credentials": [{"type": "password", "value": "adminpassword"}], "clientRoles": {"realm-management": ["manage-users"]}}, {"username": "portal-user-de", "email": "portal-user-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "User Tenant DE", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-user"]}, {"username": "portal-admin-de", "email": "portal-admin-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant DE Country DE", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-admin"]}, {"username": "portal-admin2-de", "email": "portal-admin2-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant DE Country DE 2", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-admin"]}, {"username": "portal-admin-de-lu", "email": "portal-admin-de-lu@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant DE Country LU", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-admin"]}, {"username": "portal-admin-de-both", "email": "portal-admin-de-both@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant DE Country DE LU", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-admin"]}, {"username": "portal-country-admin-de", "email": "portal-country-admin-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant <PERSON>", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-country-admin"]}, {"username": "portal-support-de", "email": "portal-support-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "Support Tenant DE", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-support-user"]}, {"username": "portal-faq-admin-de", "email": "portal-faq-admin-de@prezero", "enabled": true, "firstName": "Portal", "lastName": "FAQ Admin Tenant <PERSON>", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-de", "/hermes-portal-faq-admin"]}, {"username": "portal-admin-nl", "email": "portal-admin-nl@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant NL", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-nl", "/hermes-portal-admin"]}, {"username": "portal-admin-es", "email": "portal-admin-es@prezero", "enabled": true, "firstName": "Portal", "lastName": "Admin Tenant ES", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-es", "/hermes-portal-admin"]}, {"username": "service-account-hermes-backend", "emailVerified": false, "enabled": true, "serviceAccountClientId": "hermes-backend", "requiredActions": [], "clientRoles": {"hermes-iam": ["full-access"]}, "groups": []}, {"username": "service-account-iot-backend", "emailVerified": false, "enabled": true, "serviceAccountClientId": "iot-backend", "requiredActions": [], "clientRoles": {"hermes-iam": ["full-access"]}, "groups": []}, {"username": "service-account-delphi-backend", "emailVerified": false, "enabled": true, "serviceAccountClientId": "delphi-backend", "requiredActions": [], "clientRoles": {"hermes-iam": ["full-access"]}, "groups": []}, {"username": "service-account-hermes-iam", "emailVerified": false, "enabled": true, "serviceAccountClientId": "hermes-iam", "requiredActions": [], "clientRoles": {"realm-management": ["manage-users"]}, "groups": []}]}