FROM ghcr.io/prezero/hermes-backend-images/base-php-dev:4.11.0@sha256:8d87dc8f39efd35da7ce176a531abe1f5816bc51990f08177ee986e7cdfb5674

RUN rm -f /usr/local/etc/php/conf.d/docker-php-ext-blackfire.ini

WORKDIR /application

COPY --link docker/php.ini $PHP_INI_DIR/conf.d/z-app.ini
COPY --link . ./
COPY --link docker/dev-k8s/init-script.sh /init-script.sh

RUN rm -Rf .github docker .git .dockerignore .bp-config .gitignore docker-compose.yml

ENV CAPTAINHOOK_DISABLE=true

# Set -e (stop the script on error), -u (fail if an undefined variable is used) and -x (print each command before executing it)
RUN --mount=type=secret,id=COMPOSER_AUTH,env=COMPOSER_AUTH set -eux \
    && composer install --no-cache --no-scripts --no-autoloader \
    && composer dump-autoload --classmap-authoritative \
    && chmod +x bin/console \
    && bin/console assets:install \
    && chown -R user:user /application
