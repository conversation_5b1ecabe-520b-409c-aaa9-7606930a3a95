<?php

declare(strict_types=1);

namespace App\Domain\Client;

use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class ClientGroupsService
{
    /**
     * @param array<string, string[]> $clientGroupsConfiguration
     * @param string[]                $uniqueTenantGroups
     * @param string[]                $protectedGroups
     */
    public function __construct(
        #[Autowire('%app.client-groups%')]
        private array $clientGroupsConfiguration,
        #[Autowire('%app.unique-tenant-groups%')]
        private array $uniqueTenantGroups,
        #[Autowire('%app.protected-groups%')]
        private array $protectedGroups,
    ) {
    }

    /**
     * @return string[]
     */
    public function getAllowedGroupsForClient(Client $client): array
    {
        return $this->clientGroupsConfiguration[$client->id] ?? [];
    }

    /**
     * @return string[]
     */
    public function getUniqueTenantGroups(): array
    {
        return $this->uniqueTenantGroups;
    }

    /**
     * @return string[]
     */
    public function getProtectedGroups(): array
    {
        return $this->protectedGroups;
    }
}
