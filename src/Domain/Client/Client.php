<?php

declare(strict_types=1);

namespace App\Domain\Client;

use App\Domain\Client\Exception\UnrecognizedClientException;

readonly class Client
{
    private const array ALLOWED_CLIENT_IDS = [
        'hermes-backend',
        'iot-backend',
        'delphi-backend',
    ];

    public function __construct(
        public string $id,
    ) {
        $this->validate();
    }

    private function validate(): void
    {
        if (!in_array($this->id, self::ALLOWED_CLIENT_IDS, true)) {
            throw new UnrecognizedClientException(sprintf(
                'Unrecognized client id: %s',
                $this->id
            ));
        }
    }
}
