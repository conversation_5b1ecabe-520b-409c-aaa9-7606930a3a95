<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\Client\Client;
use App\Domain\Client\ClientGroupsService;

readonly class GroupAuthorizationService
{
    public function __construct(
        private ClientGroupsService $clientGroupsService,
    ) {
    }

    /**
     * Filter groups for user creation - only allow groups that the client is authorized to manage.
     *
     * @param string[] $requestedGroups
     *
     * @return string[]
     */
    public function filterAuthorizedGroupsForCreation(Client $client, array $requestedGroups): array
    {
        $allowedGroups = $this->clientGroupsService->getAllowedGroupsForClient($client);

        return array_values(array_intersect($requestedGroups, $allowedGroups));
    }

    /**
     * Filter groups for user update - preserve existing groups that are outside client scope,
     * only allow modification of groups that the client is authorized to manage.
     *
     * @param string[] $currentGroups   Current user groups
     * @param string[] $requestedGroups Requested new groups
     *
     * @return string[] Final groups (preserving unauthorized + applying authorized changes)
     */
    public function filterGroupsForUpdate(Client $client, array $currentGroups, array $requestedGroups): array
    {
        $allowedGroups = $this->clientGroupsService->getAllowedGroupsForClient($client);

        // Groups that are outside client's jurisdiction - these must be preserved
        $preservedGroups = array_diff($currentGroups, $allowedGroups);

        // Groups that the client is authorized to manage from the request
        $authorizedRequestedGroups = array_intersect($requestedGroups, $allowedGroups);

        // Combine preserved groups with authorized requested groups
        $finalGroups = array_merge($preservedGroups, $authorizedRequestedGroups);

        // Remove duplicates and return
        return array_values(array_unique($finalGroups));
    }
}
