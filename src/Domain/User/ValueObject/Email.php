<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObject;

use App\Domain\User\Constraints;
use App\Domain\User\Exception\InvalidEmailException;

readonly class Email
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new InvalidEmailException('Email cannot be empty');
        }

        if (!preg_match('/^'.Constraints::EMAIL_REGEX.'$/', $value)) {
            throw new InvalidEmailException('Invalid email address');
        }
    }
}
