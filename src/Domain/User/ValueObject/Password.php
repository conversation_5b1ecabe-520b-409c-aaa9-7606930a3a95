<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObject;

use App\Domain\User\Exception\InvalidPasswordException;

readonly class Password
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new InvalidPasswordException('Password cannot be empty');
        }
    }
}
