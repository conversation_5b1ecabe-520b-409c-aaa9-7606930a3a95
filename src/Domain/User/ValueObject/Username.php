<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObject;

use App\Domain\User\Constraints;
use App\Domain\User\Exception\InvalidUsernameException;

readonly class Username
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new InvalidUsernameException('Username cannot be empty');
        }

        if (strlen($value) > 250) {
            throw new InvalidUsernameException('Username cannot be longer than 250 characters');
        }

        if (!preg_match('/^'.Constraints::USERNAME_REGEX.'$/', $value)) {
            throw new InvalidUsernameException('Username contains invalid characters');
        }
    }
}
