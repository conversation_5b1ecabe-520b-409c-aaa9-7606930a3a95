<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObject;

use App\Domain\User\Constraints;
use App\Domain\User\Exception\InvalidNameException;

readonly class Name
{
    public function __construct(
        public string $value,
    ) {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new InvalidNameException('Name cannot be empty');
        }

        if (strlen($value) > 250) {
            throw new InvalidNameException('Name cannot be longer than 250 characters');
        }

        if (!preg_match('/^'.Constraints::NAME_REGEX.'$/', $value)) {
            throw new InvalidNameException('Name contains invalid characters');
        }
    }
}
