<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\Client\Client;
use App\Domain\User\Enum\RequiredAction;
use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Password;
use App\Domain\User\ValueObject\Username;

readonly class UserManagementService
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private GroupAuthorizationService $groupAuthorizationService,
        private GroupValidationService $groupValidationService,
    ) {
    }

    public function checkUsernameExists(Username $username): bool
    {
        return $this->userRepository->usernameExists($username);
    }

    public function checkEmailExists(Email $email): bool
    {
        return $this->userRepository->emailExists($email);
    }

    public function getUserByUsername(Username $username): User
    {
        return $this->userRepository->findByUsername($username);
    }

    /**
     * Create a new user with client-scoped group authorization.
     *
     * @param string[]         $groups
     * @param RequiredAction[] $requiredActions
     *
     * @throws Exception\UserAlreadyExistsException
     * @throws Exception\UserCreationFailedException
     * @throws Exception\MultipleTenantGroupsException
     * @throws Exception\InvalidRequiredActionException
     */
    public function createUser(
        Client $client,
        Username $username,
        Email $email,
        Name $firstName,
        Name $lastName,
        Password $password,
        array $groups = [],
        array $requiredActions = [],
    ): User {
        // Check if username already exists
        if ($this->userRepository->usernameExists($username)) {
            throw new Exception\UserAlreadyExistsException("User with username '{$username->value}' already exists");
        }

        // Check if email already exists
        if ($this->userRepository->emailExists($email)) {
            throw new Exception\UserAlreadyExistsException("User with email '{$email->value}' already exists");
        }

        // Filter groups based on client authorization
        $authorizedGroups = $this->groupAuthorizationService->filterAuthorizedGroupsForCreation($client, $groups);

        // Validate group assignment rules
        $this->groupValidationService->validateUniqueTenantGroups($authorizedGroups);

        RequiredActions::validate($requiredActions);

        return $this->userRepository->createUser($username, $email, $firstName, $lastName, $password, $authorizedGroups, $requiredActions);
    }

    /**
     * Update an existing user with client-scoped group authorization.
     * Preserves groups outside client scope while allowing modification of authorized groups.
     *
     * @param string[]|null $groups
     *
     * @throws Exception\ProtectedGroupModificationException
     * @throws Exception\MultipleTenantGroupsException
     */
    public function updateUser(
        Client $client,
        Username $username,
        ?Email $email = null,
        ?Name $firstName = null,
        ?Name $lastName = null,
        ?array $groups = null,
    ): User {
        $existingUser = $this->userRepository->findByUsername($username);

        if (
            null !== $email
            && $email->value !== $existingUser->email->value
            && $this->userRepository->emailExists($email)
        ) {
            throw new Exception\UserAlreadyExistsException("User with email '{$email->value}' already exists");
        }

        // Handle group authorization if groups are being updated
        $finalGroups = $groups;

        if (null !== $groups) {
            // Validate that protected groups are not being removed
            $this->groupValidationService->validateProtectedGroupsNotRemoved(
                $existingUser->groups,
                $groups,
                $client
            );

            // Filter groups based on client authorization, preserving unauthorized groups
            $finalGroups = $this->groupAuthorizationService->filterGroupsForUpdate(
                $client,
                $existingUser->groups,
                $groups
            );

            // Validate that final groups don't contain multiple tenant groups
            $this->groupValidationService->validateUniqueTenantGroups($finalGroups);
        }

        return $this->userRepository->updateUser($existingUser, $email, $firstName, $lastName, $finalGroups);
    }

    /**
     * Update a user's password.
     *
     * @throws Exception\UserNotFoundException
     * @throws Exception\UserUpdateFailedException
     */
    public function updateUserPassword(Username $username, Password $password): void
    {
        $this->userRepository->updateUserPassword($username, $password);
    }
}
