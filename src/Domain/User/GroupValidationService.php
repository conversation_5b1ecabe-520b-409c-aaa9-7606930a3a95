<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\Client\Client;
use App\Domain\Client\ClientGroupsService;
use App\Domain\User\Exception\MultipleTenantGroupsException;
use App\Domain\User\Exception\ProtectedGroupModificationException;

readonly class GroupValidationService
{
    public function __construct(
        private ClientGroupsService $clientGroupsService,
    ) {
    }

    /**
     * Validate that groups contain at most one unique tenant group.
     *
     * @param string[] $groups
     *
     * @throws MultipleTenantGroupsException
     */
    public function validateUniqueTenantGroups(array $groups): void
    {
        $uniqueTenantGroups = $this->clientGroupsService->getUniqueTenantGroups();
        $assignedTenantGroups = array_intersect($groups, $uniqueTenantGroups);

        if (count($assignedTenantGroups) > 1) {
            throw new MultipleTenantGroupsException(
                'Only one tenant group can be assigned to a user at a time. Found: '.implode(', ', $assignedTenantGroups)
            );
        }
    }

    /**
     * Validate that no protected groups are being removed from the user.
     *
     * @param string[] $currentGroups   Current user groups
     * @param string[] $requestedGroups Requested new groups
     *
     * @throws ProtectedGroupModificationException
     */
    public function validateProtectedGroupsNotRemoved(array $currentGroups, array $requestedGroups, Client $client): void
    {
        $allowedGroups = $this->clientGroupsService->getAllowedGroupsForClient($client);
        $protectedGroups = $this->clientGroupsService->getProtectedGroups();

        // Find protected groups that are currently assigned and within client scope
        $currentProtectedGroups = array_intersect($currentGroups, $protectedGroups, $allowedGroups);

        // Find protected groups that would remain after the update (within client scope)
        $requestedProtectedGroups = array_intersect($requestedGroups, $protectedGroups, $allowedGroups);

        // Check if any protected groups are being removed
        $removedProtectedGroups = array_diff($currentProtectedGroups, $requestedProtectedGroups);

        if (!empty($removedProtectedGroups)) {
            throw new ProtectedGroupModificationException(
                'Protected groups cannot be removed once assigned: '.implode(', ', $removedProtectedGroups)
            );
        }
    }
}
