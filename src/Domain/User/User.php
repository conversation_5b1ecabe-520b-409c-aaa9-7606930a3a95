<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Username;

readonly class User
{
    /**
     * @param string[]                $groups
     * @param string[]                $realmRoles
     * @param array<string, string[]> $clientRoles
     */
    public function __construct(
        public Username $username,
        public Email $email,
        public Name $firstName,
        public Name $lastName,
        public array $groups = [],
        public array $realmRoles = [],
        public array $clientRoles = [],
        public ?string $id = null,
    ) {
    }
}
