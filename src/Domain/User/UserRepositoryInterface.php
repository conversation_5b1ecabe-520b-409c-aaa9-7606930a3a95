<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\User\Enum\RequiredAction;
use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Password;
use App\Domain\User\ValueObject\Username;

interface UserRepositoryInterface
{
    /**
     * Check if a username exists in the system.
     */
    public function usernameExists(Username $username): bool;

    /**
     * Check if an email exists in the system.
     */
    public function emailExists(Email $email): bool;

    /**
     * Find a user by username.
     *
     * @throws Exception\UserNotFoundException
     */
    public function findByUsername(Username $username): User;

    /**
     * Find a user by email.
     *
     * @throws Exception\UserNotFoundException
     */
    public function findByEmail(Email $email): User;

    /**
     * Create a new user.
     *
     * @param string[]         $groups
     * @param RequiredAction[] $requiredActions
     *
     * @throws Exception\UserCreationFailedException
     */
    public function createUser(
        Username $username,
        Email $email,
        Name $firstName,
        Name $lastName,
        Password $password,
        array $groups = [],
        array $requiredActions = [],
    ): User;

    /**
     * Update an existing user.
     *
     * @param string[]|null $groups
     *
     * @throws Exception\UserUpdateFailedException
     * @throws Exception\UserNotFoundException
     */
    public function updateUser(
        User $existingUser,
        ?Email $email = null,
        ?Name $firstName = null,
        ?Name $lastName = null,
        ?array $groups = null,
    ): User;

    /**
     * Update a user's password.
     *
     * @throws Exception\UserUpdateFailedException
     * @throws Exception\UserNotFoundException
     */
    public function updateUserPassword(Username $username, Password $password): void;
}
