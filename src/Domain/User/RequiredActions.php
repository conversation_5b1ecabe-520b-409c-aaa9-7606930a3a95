<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\User\Enum\RequiredAction;
use App\Domain\User\Exception\InvalidRequiredActionException;

readonly class RequiredActions
{
    /**
     * Validate an array of required actions.
     *
     * @param RequiredAction[] $requiredActions
     *
     * @throws InvalidRequiredActionException
     */
    public static function validate(array $requiredActions): void
    {
        foreach ($requiredActions as $requiredAction) {
            if (!$requiredAction instanceof RequiredAction) { // @phpstan-ignore-line
                throw new InvalidRequiredActionException('Invalid required action given');
            }
        }

        $requiredActions = array_map(static fn (RequiredAction $action): string => $action->value, $requiredActions);

        // Check for duplicates
        if (count($requiredActions) !== count(array_unique($requiredActions))) {
            throw new InvalidRequiredActionException('Duplicate required actions are not allowed');
        }
    }
}
