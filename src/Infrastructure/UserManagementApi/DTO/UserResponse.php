<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\DTO;

use App\Domain\User\User;

class UserResponse
{
    public string $username;

    public string $email;

    public string $firstName;

    public string $lastName;

    /**
     * @var string[]
     */
    public array $realmRoles;

    /**
     * @var array<string, string[]>
     */
    public array $clientRoles;

    /**
     * @var array<string>
     */
    public array $groups;

    public static function fromDomain(User $user): self
    {
        $response = new self();
        $response->username = $user->username->value;
        $response->email = $user->email->value;
        $response->firstName = $user->firstName->value;
        $response->lastName = $user->lastName->value;
        $response->realmRoles = $user->realmRoles;
        $response->clientRoles = $user->clientRoles;
        $response->groups = $user->groups;

        return $response;
    }
}
