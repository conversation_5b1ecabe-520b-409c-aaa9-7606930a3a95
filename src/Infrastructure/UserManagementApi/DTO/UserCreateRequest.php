<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\DTO;

use App\Domain\User\Enum\RequiredAction;
use Symfony\Component\Validator\Constraints as Assert;

class UserCreateRequest
{
    #[Assert\NotBlank(message: 'Username is required')]
    public string $username;

    #[Assert\NotBlank(message: 'First name is required')]
    public string $firstname;

    #[Assert\NotBlank(message: 'Last name is required')]
    public string $lastname;

    #[Assert\NotBlank(message: 'Email is required')]
    public string $email;

    #[Assert\NotBlank(message: 'Password is required')]
    public string $password;

    /**
     * @var string[]
     */
    public array $groups = [];

    /**
     * Required actions that the user must complete on first login.
     *
     * @var RequiredAction[]
     */
    public array $requiredActions = [];
}
