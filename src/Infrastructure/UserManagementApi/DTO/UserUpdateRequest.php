<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\DTO;

use Symfony\Component\Validator\Constraints as Assert;

class UserUpdateRequest
{
    #[Assert\Length(max: 250, maxMessage: 'First name cannot be longer than 250 characters')]
    public ?string $firstname = null;

    #[Assert\Length(max: 250, maxMessage: 'Last name cannot be longer than 250 characters')]
    public ?string $lastname = null;

    public ?string $email = null;

    /**
     * @var string[]|null
     */
    public ?array $groups = null;
}
