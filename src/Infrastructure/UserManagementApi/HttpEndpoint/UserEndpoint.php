<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\HttpEndpoint;

use App\Domain\User\Exception\InvalidEmailException;
use App\Domain\User\Exception\InvalidNameException;
use App\Domain\User\Exception\InvalidPasswordException;
use App\Domain\User\Exception\InvalidRequiredActionException;
use App\Domain\User\Exception\InvalidUsernameException;
use App\Domain\User\Exception\MultipleTenantGroupsException;
use App\Domain\User\Exception\ProtectedGroupModificationException;
use App\Domain\User\Exception\UserAlreadyExistsException;
use App\Domain\User\Exception\UserCreationFailedException;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\Exception\UserUpdateFailedException;
use App\Domain\User\UserManagementService;
use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Password;
use App\Domain\User\ValueObject\Username;
use App\Infrastructure\Framework\Symfony\Security\Security;
use App\Infrastructure\UserManagementApi\DTO\UserCreateRequest;
use App\Infrastructure\UserManagementApi\DTO\UserPasswordUpdateRequest;
use App\Infrastructure\UserManagementApi\DTO\UserResponse;
use App\Infrastructure\UserManagementApi\DTO\UserUpdateRequest;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

#[AsController]
readonly class UserEndpoint
{
    public function __construct(
        private UserManagementService $userManagementService,
        private Security $security,
    ) {
    }

    public function checkUsername(string $username): Response
    {
        try {
            $username = new Username($username);
        } catch (InvalidUsernameException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        if ($this->userManagementService->checkUsernameExists($username)) {
            return new Response(status: 200);
        }

        throw new NotFoundHttpException('Username not found');
    }

    public function checkEmail(string $email): Response
    {
        try {
            $email = new Email($email);
        } catch (InvalidEmailException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        if ($this->userManagementService->checkEmailExists($email)) {
            return new Response(status: 200);
        }

        throw new NotFoundHttpException('Email not found');
    }

    public function getByUsername(string $username): UserResponse
    {
        try {
            $username = new Username($username);
        } catch (InvalidUsernameException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        try {
            $user = $this->userManagementService->getUserByUsername($username);
        } catch (UserNotFoundException $e) {
            throw new NotFoundHttpException($e->getMessage());
        }

        return UserResponse::fromDomain($user);
    }

    public function createUser(UserCreateRequest $request): UserResponse
    {
        try {
            $username = new Username($request->username);
            $email = new Email($request->email);
            $firstName = new Name($request->firstname);
            $lastName = new Name($request->lastname);
            $password = new Password($request->password);
        } catch (InvalidUsernameException|InvalidEmailException|InvalidNameException|InvalidPasswordException|InvalidRequiredActionException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        try {
            $client = $this->security->getCurrentClient();

            $user = $this->userManagementService->createUser(
                $client,
                $username,
                $email,
                $firstName,
                $lastName,
                $password,
                $request->groups,
                $request->requiredActions,
            );
        } catch (UserAlreadyExistsException $e) {
            throw new ConflictHttpException($e->getMessage());
        } catch (UserCreationFailedException|MultipleTenantGroupsException|ProtectedGroupModificationException|InvalidRequiredActionException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return UserResponse::fromDomain($user);
    }

    public function updateUser(string $username, UserUpdateRequest $request): UserResponse
    {
        try {
            $usernameVO = new Username($username);
        } catch (InvalidUsernameException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        $email = null;
        $firstName = null;
        $lastName = null;

        try {
            if (null !== $request->email) {
                $email = new Email($request->email);
            }

            if (null !== $request->firstname) {
                $firstName = new Name($request->firstname);
            }

            if (null !== $request->lastname) {
                $lastName = new Name($request->lastname);
            }
        } catch (InvalidEmailException|InvalidNameException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        try {
            $client = $this->security->getCurrentClient();

            $user = $this->userManagementService->updateUser(
                $client,
                $usernameVO,
                $email,
                $firstName,
                $lastName,
                $request->groups
            );
        } catch (UserNotFoundException $e) {
            throw new NotFoundHttpException($e->getMessage());
        } catch (UserAlreadyExistsException $e) {
            throw new ConflictHttpException($e->getMessage());
        } catch (UserUpdateFailedException|MultipleTenantGroupsException|ProtectedGroupModificationException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return UserResponse::fromDomain($user);
    }

    public function updateUserPassword(string $username, UserPasswordUpdateRequest $request): Response
    {
        try {
            $usernameVO = new Username($username);
            $password = new Password($request->password);

            $this->userManagementService->updateUserPassword($usernameVO, $password);
        } catch (InvalidUsernameException|InvalidPasswordException $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        } catch (UserNotFoundException $e) {
            throw new NotFoundHttpException($e->getMessage());
        } catch (UserUpdateFailedException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return new Response('', Response::HTTP_OK);
    }
}
