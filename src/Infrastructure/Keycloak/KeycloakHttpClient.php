<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak;

use App\Infrastructure\Framework\Serializer\SerializerException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\Keycloak\DTO\AccessTokenResponse;
use App\Infrastructure\Keycloak\DTO\GroupResponse;
use App\Infrastructure\Keycloak\DTO\RoleMappingResponse;
use App\Infrastructure\Keycloak\DTO\UserCreateRequest;
use App\Infrastructure\Keycloak\DTO\UserResponse;
use App\Infrastructure\Keycloak\DTO\UserUpdateRequest;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class KeycloakHttpClient
{
    private ?string $accessToken = null;
    private ?int $accessTokenExpiresAt = null;

    public function __construct(
        private readonly HttpClientInterface $keycloakClient,

        private readonly LoggerInterface $logger,

        private readonly SerializerInterface $serializer,

        #[Autowire('%env(KEYCLOAK_REALM)%')]
        private readonly string $keycloakRealm,

        #[Autowire('%env(KEYCLOAK_CLIENT_ID)%')]
        private readonly string $clientId,

        #[Autowire('%env(KEYCLOAK_CLIENT_SECRET)%')]
        private readonly string $clientSecret,
    ) {
    }

    public function findByUsername(string $username): ?UserResponse
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: $this->adminEndpoint('/users'),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                    'query' => [
                        'username' => $username,
                        'exact' => 'true',
                    ],
                ]
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to find user by username',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to find user by username');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to find user by username', ['exception' => $e]);

            throw new \RuntimeException('Failed to find user by username', 0, $e);
        }

        try {
            $users = $this->serializer->deserializeIntoArrayOfObjects($responseContent, UserResponse::class);

            return 1 === count($users) ? $users[0] : null;
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user representation.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak user representation.', 0, $e);
        }
    }

    public function findByEmail(string $email): ?UserResponse
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: $this->adminEndpoint('/users'),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                    'query' => [
                        'email' => $email,
                        'exact' => 'true',
                    ],
                ]
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to find user by email',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to find user by email');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to find user by email', ['exception' => $e]);

            throw new \RuntimeException('Failed to find user by email', 0, $e);
        }

        try {
            $users = $this->serializer->deserializeIntoArrayOfObjects($responseContent, UserResponse::class);

            return 1 === count($users) ? $users[0] : null;
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user representation.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak user representation.', 0, $e);
        }
    }

    /**
     * @return GroupResponse[]
     */
    public function getGroupsForUser(string $userId): array
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: $this->adminEndpoint("/users/{$userId}/groups"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                ]
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user groups',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to get user groups');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to get user groups', ['exception' => $e]);

            throw new \RuntimeException('Failed to get user groups', 0, $e);
        }

        try {
            return $this->serializer->deserializeIntoArrayOfObjects($responseContent, GroupResponse::class);
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak group representation.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak group representation.', 0, $e);
        }
    }

    public function getRoleMappingsForUser(string $userId): RoleMappingResponse
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: $this->adminEndpoint("/users/{$userId}/role-mappings"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                ]
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user role mappings',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to get user role mappings');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to get user role mappings', ['exception' => $e]);

            throw new \RuntimeException('Failed to get user role mappings', 0, $e);
        }

        try {
            return $this->serializer->deserializeIntoObject($responseContent, RoleMappingResponse::class);
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak role mapping representation.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak role mapping representation.', 0, $e);
        }
    }

    private function adminEndpoint(string $path): string
    {
        return "/admin/realms/{$this->keycloakRealm}{$path}";
    }

    private function getAccessToken(): string
    {
        if (
            null !== $this->accessToken
            && null !== $this->accessTokenExpiresAt
            && $this->accessTokenExpiresAt > time() - 10
        ) {
            $this->logger->debug(
                'Using cached Keycloak Client access token.',
                ['expires_at' => $this->accessTokenExpiresAt]
            );

            return $this->accessToken;
        }

        $this->logger->debug('Retrieving new Keycloak Client access token.');

        try {
            $response = $this->keycloakClient->request(
                method: 'POST',
                url: "/realms/{$this->keycloakRealm}/protocol/openid-connect/token",
                options: [
                    'body' => [
                        'client_id' => $this->clientId,
                        'client_secret' => $this->clientSecret,
                        'grant_type' => 'client_credentials',
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to retrieve access token from Keycloak',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to retrieve access token from Keycloak');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to retrieve access token from Keycloak', ['exception' => $e]);

            throw new \RuntimeException('Failed to retrieve access token from Keycloak', 0, $e);
        }

        try {
            $accessTokenResponse = $this->serializer->deserializeIntoObject(
                data: $responseContent,
                className: AccessTokenResponse::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak client authentication response.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak client authentication response.', 0, $e);
        }

        $this->accessToken = $accessTokenResponse->accessToken;
        $this->accessTokenExpiresAt = time() + $accessTokenResponse->expiresIn;

        return $this->accessToken;
    }

    public function createUser(UserCreateRequest $userCreateRequest): string
    {
        $body = $this->serializer->serialize($userCreateRequest, 'json');

        try {
            $response = $this->keycloakClient->request(
                method: 'POST',
                url: $this->adminEndpoint('/users'),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                        'Content-Type' => 'application/json',
                    ],
                    'body' => $body,
                ]
            );

            if (201 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to create user',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to create user');
            }

            // Extract user ID from Location header
            $locationHeader = $response->getHeaders()['location'][0] ?? '';
            $userId = basename($locationHeader);

            if (empty($userId)) {
                throw new \RuntimeException('Failed to extract user ID from response');
            }

            return $userId;
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to create user', ['exception' => $e]);

            throw new \RuntimeException('Failed to create user', 0, $e);
        }
    }

    public function updateUser(string $userId, UserUpdateRequest $userUpdateRequest): void
    {
        $updateData = [];

        if (null !== $userUpdateRequest->email) {
            $updateData['email'] = $userUpdateRequest->email;
        }

        if (null !== $userUpdateRequest->firstName) {
            $updateData['firstName'] = $userUpdateRequest->firstName;
        }

        if (null !== $userUpdateRequest->lastName) {
            $updateData['lastName'] = $userUpdateRequest->lastName;
        }

        if (null !== $userUpdateRequest->enabled) {
            $updateData['enabled'] = $userUpdateRequest->enabled;
        }

        if (null !== $userUpdateRequest->emailVerified) {
            $updateData['emailVerified'] = $userUpdateRequest->emailVerified;
        }

        if (empty($updateData)) {
            return; // Nothing to update
        }

        try {
            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: $this->adminEndpoint("/users/{$userId}"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $updateData,
                ]
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to update user',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to update user');
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to update user', ['exception' => $e]);

            throw new \RuntimeException('Failed to update user', 0, $e);
        }
    }

    public function resetUserPassword(string $userId, string $password): void
    {
        $credentialData = [
            'type' => 'password',
            'value' => $password,
            'temporary' => false,
        ];

        try {
            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: $this->adminEndpoint("/users/{$userId}/reset-password"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $credentialData,
                ]
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to reset user password',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to reset user password');
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to reset user password', ['exception' => $e]);

            throw new \RuntimeException('Failed to reset user password', 0, $e);
        }
    }

    /**
     * @return GroupResponse[]
     */
    public function getAllGroups(): array
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: $this->adminEndpoint('/groups'),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                ]
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get all groups',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to get all groups');
            }

            $responseContent = $response->getContent();
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to get all groups', ['exception' => $e]);

            throw new \RuntimeException('Failed to get all groups', 0, $e);
        }

        try {
            return $this->serializer->deserializeIntoArrayOfObjects($responseContent, GroupResponse::class, 'json');
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak group representation.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException('Failed to deserialize Keycloak group representation.', 0, $e);
        }
    }

    public function addUserToGroup(string $userId, string $groupId): void
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: $this->adminEndpoint("/users/{$userId}/groups/{$groupId}"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                ]
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to add user to group',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to add user to group');
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to add user to group', ['exception' => $e]);

            throw new \RuntimeException('Failed to add user to group', 0, $e);
        }
    }

    public function removeUserFromGroup(string $userId, string $groupId): void
    {
        try {
            $response = $this->keycloakClient->request(
                method: 'DELETE',
                url: $this->adminEndpoint("/users/{$userId}/groups/{$groupId}"),
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->getAccessToken(),
                    ],
                ]
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to remove user from group',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException('Failed to remove user from group');
            }
        } catch (ExceptionInterface $e) {
            $this->logger->critical('Failed to remove user from group', ['exception' => $e]);

            throw new \RuntimeException('Failed to remove user from group', 0, $e);
        }
    }
}
