<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\Mapper;

use App\Domain\User\User;
use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Username;
use App\Infrastructure\Keycloak\DTO\GroupResponse;
use App\Infrastructure\Keycloak\DTO\RoleMappingResponse;
use App\Infrastructure\Keycloak\DTO\RoleResponse;
use App\Infrastructure\Keycloak\DTO\UserResponse;

readonly class UserMapper
{
    /**
     * @param GroupResponse[] $keycloakGroups
     */
    public function constructDomainUser(
        UserResponse $keycloakUser,
        array $keycloakGroups,
        RoleMappingResponse $keycloakRoleMappings,
    ): User {
        $groups = array_map(
            static fn (GroupResponse $group): string => $group->name,
            $keycloakGroups,
        );

        $realmRoles = array_map(
            static fn (RoleResponse $role): string => $role->name,
            $keycloakRoleMappings->realmMappings,
        );

        $clientRoles = [];

        foreach ($keycloakRoleMappings->clientMappings as $clientMapping) {
            $clientRoles[$clientMapping->client] = array_map(
                static fn (RoleResponse $role): string => $role->name,
                $clientMapping->mappings,
            );
        }

        return new User(
            username: new Username($keycloakUser->username),
            email: new Email($keycloakUser->email ?? ''),
            firstName: new Name($keycloakUser->firstName ?? ''),
            lastName: new Name($keycloakUser->lastName ?? ''),
            groups: $groups,
            realmRoles: $realmRoles,
            clientRoles: $clientRoles,
            id: $keycloakUser->id,
        );
    }
}
