<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak;

use App\Domain\User\Exception\UserCreationFailedException;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\Exception\UserUpdateFailedException;
use App\Domain\User\User;
use App\Domain\User\UserRepositoryInterface;
use App\Domain\User\ValueObject\Email;
use App\Domain\User\ValueObject\Name;
use App\Domain\User\ValueObject\Password;
use App\Domain\User\ValueObject\Username;
use App\Infrastructure\Keycloak\DTO\CredentialRepresentation;
use App\Infrastructure\Keycloak\DTO\UserCreateRequest;
use App\Infrastructure\Keycloak\DTO\UserUpdateRequest;
use App\Infrastructure\Keycloak\Mapper\UserMapper;

readonly class KeycloakUserRepository implements UserRepositoryInterface
{
    public function __construct(
        private KeycloakHttpClient $httpClient,
        private UserMapper $userMapper,
    ) {
    }

    public function usernameExists(Username $username): bool
    {
        return null !== $this->httpClient->findByUsername($username->value);
    }

    public function emailExists(Email $email): bool
    {
        return null !== $this->httpClient->findByEmail($email->value);
    }

    public function findByUsername(Username $username): User
    {
        $keycloakUser = $this->httpClient->findByUsername($username->value);

        if (null === $keycloakUser) {
            throw new UserNotFoundException("User with username '{$username->value}' not found");
        }

        $keycloakGroups = $this->httpClient->getGroupsForUser($keycloakUser->id);
        $roleMappings = $this->httpClient->getRoleMappingsForUser($keycloakUser->id);

        return $this->userMapper->constructDomainUser($keycloakUser, $keycloakGroups, $roleMappings);
    }

    public function findByEmail(Email $email): User
    {
        $keycloakUser = $this->httpClient->findByEmail($email->value);

        if (null === $keycloakUser) {
            throw new UserNotFoundException("User with email '{$email->value}' not found");
        }

        $keycloakGroups = $this->httpClient->getGroupsForUser($keycloakUser->id);
        $roleMappings = $this->httpClient->getRoleMappingsForUser($keycloakUser->id);

        return $this->userMapper->constructDomainUser($keycloakUser, $keycloakGroups, $roleMappings);
    }

    public function createUser(
        Username $username,
        Email $email,
        Name $firstName,
        Name $lastName,
        Password $password,
        array $groups = [],
        array $requiredActions = [],
    ): User {
        try {
            // Create user in Keycloak
            $userCreateRequest = new UserCreateRequest(
                username: $username->value,
                email: $email->value,
                firstName: $firstName->value,
                lastName: $lastName->value,
                credentials: [
                    new CredentialRepresentation(
                        value: $password->value,
                        type: 'password',
                        temporary: false,
                    ),
                ],
                groups: array_map(static fn (string $group) => '/'.$group, $groups),
                requiredActions: $requiredActions,
            );

            $this->httpClient->createUser($userCreateRequest);

            // Return the created user
            return $this->findByUsername($username);
        } catch (\Exception $e) {
            throw new UserCreationFailedException(
                "Failed to create user '{$username->value}': {$e->getMessage()}",
                0,
                $e
            );
        }
    }

    public function updateUser(
        User $existingUser,
        ?Email $email = null,
        ?Name $firstName = null,
        ?Name $lastName = null,
        ?array $groups = null,
    ): User {
        if (null === $existingUser->id) {
            throw new \RuntimeException('User ID is missing');
        }

        // Update basic user information
        $userUpdateRequest = new UserUpdateRequest(
            email: $email?->value,
            firstName: $firstName?->value,
            lastName: $lastName?->value,
        );

        try {
            $this->httpClient->updateUser($existingUser->id, $userUpdateRequest);

            // Update groups if specified
            if (null !== $groups) {
                $this->updateUserGroups($existingUser->id, $groups);
            }

            // Return the updated user
            return $this->findByUsername($existingUser->username);
        } catch (UserNotFoundException $e) {
            throw $e; // Re-throw as is
        } catch (\Exception $e) {
            throw new UserUpdateFailedException(
                "Failed to update user '{$existingUser->username->value}': {$e->getMessage()}",
                0,
                $e
            );
        }
    }

    public function updateUserPassword(Username $username, Password $password): void
    {
        // First, find the user to get their ID
        $keycloakUser = $this->httpClient->findByUsername($username->value);

        if (null === $keycloakUser) {
            throw new UserNotFoundException("User with username '{$username->value}' not found");
        }

        try {
            $this->httpClient->resetUserPassword($keycloakUser->id, $password->value);
        } catch (\Exception $e) {
            throw new UserUpdateFailedException(
                "Failed to update password for user '{$username->value}': {$e->getMessage()}",
                0,
                $e
            );
        }
    }

    /**
     * @param string[] $newGroupNames
     */
    private function updateUserGroups(string $userId, array $newGroupNames): void
    {
        // Get current user groups
        $currentGroups = $this->httpClient->getGroupsForUser($userId);
        $currentGroupNames = array_map(static fn ($group) => $group->name, $currentGroups);

        // Get all available groups
        $allGroups = $this->httpClient->getAllGroups();
        $groupMap = [];

        foreach ($allGroups as $group) {
            $groupMap[$group->name] = $group->id;
        }

        // Remove user from groups that are no longer needed
        $groupsToRemove = array_diff($currentGroupNames, $newGroupNames);
        foreach ($groupsToRemove as $groupName) {
            if (isset($groupMap[$groupName])) {
                $this->httpClient->removeUserFromGroup($userId, $groupMap[$groupName]);
            }
        }

        // Add user to new groups
        $groupsToAdd = array_diff($newGroupNames, $currentGroupNames);
        foreach ($groupsToAdd as $groupName) {
            if (isset($groupMap[$groupName])) {
                $this->httpClient->addUserToGroup($userId, $groupMap[$groupName]);
            }
        }
    }
}
