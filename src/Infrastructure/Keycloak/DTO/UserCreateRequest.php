<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\DTO;

use App\Domain\User\Enum\RequiredAction;

readonly class UserCreateRequest
{
    /**
     * @param CredentialRepresentation[] $credentials
     * @param string[]                   $groups
     * @param RequiredAction[]           $requiredActions
     */
    public function __construct(
        public string $username,
        public string $email,
        public string $firstName,
        public string $lastName,
        public array $credentials,
        public bool $enabled = true,
        public bool $emailVerified = true,
        public array $groups = [],
        public array $requiredActions = [],
    ) {
    }
}
