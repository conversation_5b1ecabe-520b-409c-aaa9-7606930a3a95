<?php

declare(strict_types=1);

namespace App\Infrastructure\HomePage;

readonly class BullshitGenerator
{
    private const array ADVERBS = [
        'appropriately',
        'assertively',
        'authoritatively',
        'collaboratively',
        'compellingly',
        'competently',
        'completely',
        'continually',
        'conveniently',
        'credibly',
        'distinctively',
        'dramatically',
        'dynamically',
        'efficiently',
        'energistically',
        'enthusiastically',
        'fungibly',
        'globally',
        'holisticly',
        'interactively',
        'intrinsically',
        'monotonectally',
        'objectively',
        'phosfluorescently',
        'proactively',
        'professionally',
        'progressively',
        'quickly',
        'rapidiously',
        'seamlessly',
        'synergistically',
        'uniquely',
    ];

    private const array VERBS = [
        'actualize',
        'administrate',
        'aggregate',
        'architect',
        'benchmark',
        'brand',
        'build',
        'cloudify',
        'communicate',
        'conceptualize',
        'coordinate',
        'create',
        'cultivate',
        'customize',
        'deliver',
        'deploy',
        'develop',
        'disintermediate',
        'disseminate',
        'drive',
        'e-enable',
        'embrace',
        'empower',
        'enable',
        'engage',
        'engineer',
        'enhance',
        'envisioneer',
        'evisculate',
        'evolve',
        'existing',
        'expedite',
        'exploit',
        'extend',
        'fabricate',
        'facilitate',
        'fashion',
        'formulate',
        'foster',
        'generate',
        'grow',
        'harness',
        'impact',
        'implement',
        'incentivize',
        'incubate',
        'initiate',
        'innovate',
        'integrate',
        'iterate',
        'leverage ',
        "leverage other's",
        'maintain',
        'matrix',
        'maximize',
        'mesh',
        'monetize',
        'morph',
        'myocardinate',
        'negotiate',
        'network',
        'optimize',
        'orchestrate',
        'parallel task',
        'plagiarize',
        'pontificate',
        'predominate',
        'procrastinate',
        'productivate',
        'productize',
        'promote',
        'provide access to',
        'pursue',
        're-engineer',
        'recaptiualize',
        'reconceptualize',
        'redefine',
        'reintermediate',
        'reinvent',
        'repurpose',
        'restore',
        'revolutionize',
        'right-shore',
        'scale',
        'seize',
        'simplify',
        'strategize',
        'streamline',
        'supply',
        'syndicate',
        'synergize',
        'synthesize',
        'target',
        'transform',
        'transition',
        'underwhelm',
        'unleash',
        'utilize',
        'visualize',
        'whiteboard',
    ];

    private const array ADJECTIVES = [
        '24/365',
        '24/7',
        'accurate',
        'adaptive',
        'agile',
        'alternative',
        'an expanded array of',
        'B2B',
        'B2C',
        'backend',
        'backward-compatible',
        'best-of-breed',
        'bleeding-edge',
        'bricks-and-clicks',
        'business',
        'clicks-and-mortar',
        'client-based',
        'client-centered',
        'client-centric',
        'client-focused',
        'cloud-based',
        'cloud-centric',
        'cloud-ready',
        'cloudified',
        'collaborative',
        'compelling',
        'competitive',
        'cooperative',
        'corporate',
        'cost effective',
        'covalent',
        'cross functional',
        'cross-media',
        'cross-platform',
        'cross-unit',
        'customer directed',
        'customized',
        'cutting-edge',
        'distinctive',
        'distributed',
        'diverse',
        'dynamic',
        'e-business',
        'economically sound',
        'effective',
        'efficient',
        'elastic',
        'emerging',
        'empowered',
        'enabled',
        'end-to-end',
        'enterprise',
        'enterprise-wide',
        'equity invested',
        'error-free',
        'ethical',
        'excellent',
        'exceptional',
        'extensible',
        'extensive',
        'flexible',
        'focused',
        'frictionless',
        'front-end',
        'fully researched',
        'fully tested',
        'functional',
        'functionalized',
        'fungible',
        'future-proof',
        'global',
        'go forward',
        'goal-oriented',
        'granular',
        'high standards in',
        'high-payoff',
        'high-quality',
        'highly efficient',
        'holistic',
        'hyper-scale',
        'impactful',
        'inexpensive',
        'innovative',
        'installed base',
        'integrated',
        'interactive',
        'interdependent',
        'intermandated',
        'interoperable',
        'intuitive',
        'just in time',
        'leading-edge',
        'leveraged',
        'long-term high-impact',
        'low-risk high-yield',
        'magnetic',
        'maintainable',
        'market positioning',
        'market-driven',
        'mission-critical',
        'multidisciplinary',
        'multifunctional',
        'multimedia based',
        'next-generation',
        'on-demand',
        'one-to-one',
        'open-source',
        'optimal',
        'orthogonal',
        'out-of-the-box',
        'pandemic',
        'parallel',
        'performance based',
        'plug-and-play',
        'premier',
        'premium',
        'principle-centered',
        'proactive',
        'process-centric',
        'professional',
        'progressive',
        'prospective',
        'quality',
        'real-time',
        'reliable',
        'resource-leveling',
        'resource-maximizing',
        'resource-sucking',
        'revolutionary',
        'robust',
        'scalable',
        'seamless',
        'stand-alone',
        'standardized',
        'standards compliant',
        'state of the art',
        'sticky',
        'strategic',
        'superior',
        'sustainable',
        'synergistic',
        'tactical',
        'team building',
        'team driven',
        'technically sound',
        'timely',
        'top-line',
        'transparent',
        'turnkey',
        'ubiquitous',
        'unique',
        'user friendly',
        'user-centric',
        'value-added',
        'vertical',
        'viral',
        'virtual',
        'visionary',
        'web-enabled',
        'wireless',
        'world-class',
        'worldwide',
    ];

    private const array NOUNS = [
        'action items',
        'alignments',
        'applications',
        'architectures',
        'bandwidth',
        'benefits',
        'best practices',
        'catalysts for change',
        'channels',
        'clouds',
        'collaboration and idea-sharing',
        'communities',
        'content',
        'convergence',
        'core competencies',
        'customer service',
        'data',
        'deliverables',
        'e-business',
        'e-commerce',
        'e-markets',
        'e-services',
        'e-tailers',
        'experiences',
        'expertise',
        'functionalities',
        'fungibility',
        'growth strategies',
        'human capital',
        'ideas',
        'imperatives',
        'infomediaries',
        'information',
        'infrastructures',
        'initiatives',
        'innovation',
        'intellectual capital',
        'interfaces',
        'internal or ',
        'leadership skills',
        'leadership',
        'manufactured products',
        'markets',
        'materials',
        'meta-services',
        'methodologies',
        'methods of empowerment',
        'metrics',
        'mindshare',
        'models',
        'networks',
        'niche markets',
        'niches',
        'nosql',
        'opportunities',
        'organic',
        'outside the box',
        'outsourcing',
        'paradigms',
        'partnerships',
        'platforms',
        'portals',
        'potentialities',
        'process improvements',
        'processes',
        'products',
        'quality vectors',
        'relationships',
        'resources',
        'results',
        'ROI',
        'scenarios',
        'schemas',
        'scrums',
        'services',
        'solutions',
        'sources',
        'sources',
        'sprints',
        'storage',
        'strategic theme areas',
        'supply chains',
        'synergy',
        'systems',
        'technologies',
        'technology',
        'testing procedures',
        'thinking',
        'total linkage',
        'users',
        'value',
        'virtualization',
        'vortals',
        'web services',
        'web-readiness',
        'wins',
    ];

    public static function generateCorporateBullshit(): string
    {
        return sprintf(
            '%s %s %s %s',
            self::ADVERBS[array_rand(self::ADVERBS)],
            self::VERBS[array_rand(self::VERBS)],
            self::ADJECTIVES[array_rand(self::ADJECTIVES)],
            self::NOUNS[array_rand(self::NOUNS)]
        );
    }
}
