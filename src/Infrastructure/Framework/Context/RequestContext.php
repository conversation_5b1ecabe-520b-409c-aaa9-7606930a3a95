<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Context;

use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;
use Symfony\Component\Uid\Uuid;
use Symfony\Contracts\Service\ResetInterface;

#[Autoconfigure(public: true)]
class RequestContext implements ResetInterface, RequestContextInterface
{
    private string $generatedRequestId;
    private ?string $userIdentifier = null;

    public function __construct(
        private readonly RequestStack $requestStack,
    ) {
        $this->generatedRequestId = Uuid::v4()->toRfc4122();
    }

    #[AsEventListener]
    public function onLoginSuccess(LoginSuccessEvent $event): void
    {
        $this->userIdentifier = $event->getUser()->getUserIdentifier();
    }

    public function getAuthenticatedUserIdentifier(): ?string
    {
        return $this->userIdentifier;
    }

    public function getRequestId(): string
    {
        $request = $this->requestStack->getCurrentRequest();

        // Highest priority: request header
        if (null !== $request && $request->headers->has('x-request-id')) {
            return (string) $request->headers->get('x-request-id');
        }

        // Lowest priority: generated request ID
        return $this->generatedRequestId;
    }

    public function reset(): void
    {
        $this->userIdentifier = null;
        $this->generatedRequestId = Uuid::v4()->toRfc4122();
    }
}
