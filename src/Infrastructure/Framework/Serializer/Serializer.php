<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use App\Infrastructure\Framework\Serializer\Exception\DeserializationException;
use App\Infrastructure\Framework\Serializer\Exception\SerializationException;
use Psr\Cache\CacheItemPoolInterface;
use Vuryss\Serializer\Context;
use Vuryss\Serializer\ExceptionInterface;
use Vuryss\Serializer\Metadata\CachedMetadataExtractor;
use Vuryss\Serializer\Metadata\MetadataExtractor;

readonly class Serializer implements SerializerInterface
{
    private \Vuryss\Serializer\SerializerInterface $vuryssSerializer;

    public function __construct(
        private CacheItemPoolInterface $serializerCache,
    ) {
        $this->vuryssSerializer = new \Vuryss\Serializer\Serializer(
            metadataExtractor: new CachedMetadataExtractor(
                new MetadataExtractor(),
                $this->serializerCache
            ),
            context: [
                Context::DATETIME_FORMAT => \DateTimeInterface::RFC3339_EXTENDED,
                Context::SKIP_NULL_VALUES => true,
            ],
        );
    }

    public function serialize(mixed $data, string $format = 'json', array $context = []): string
    {
        try {
            return $this->vuryssSerializer->serialize($data, $format, $context);
        } catch (ExceptionInterface $e) {
            throw new SerializationException($e->getMessage(), previous: $e);
        }
    }

    public function deserialize(mixed $data, string $type, string $format = 'json', array $context = []): mixed
    {
        try {
            return $this->vuryssSerializer->deserialize($data, $type, $format, $context);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    public function normalize(mixed $data, array $context = []): array|string|int|float|bool|null
    {
        try {
            return $this->vuryssSerializer->normalize($data, $context);
        } catch (ExceptionInterface $e) {
            throw new SerializationException($e->getMessage(), previous: $e);
        }
    }

    public function denormalize(mixed $data, ?string $type, array $context = []): mixed
    {
        try {
            return $this->vuryssSerializer->denormalize($data, $type, $context);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object {
        try {
            return $this->vuryssSerializer->deserialize($data, $className, 'json', []);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-return array<T>
     *
     * @throws DeserializationException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array {
        try {
            /** @var array<T> $array */
            $array = $this->vuryssSerializer->deserialize($data, $className.'[]', 'json', $context);

            return $array;
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }
}
