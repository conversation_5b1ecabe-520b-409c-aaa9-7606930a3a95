<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use V<PERSON>ss\Serializer\Context;

/**
 * @phpstan-import-type ContextOptions from Context
 */
interface SerializerInterface extends \Vuryss\Serializer\SerializerInterface
{
    /**
     * Deserializes data into the given object.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return T
     *
     * @throws SerializerException
     */
    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object;

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return array<T>
     *
     * @throws SerializerException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array;
}
