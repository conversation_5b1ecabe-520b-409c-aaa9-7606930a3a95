<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Security\Keycloak;

use App\Domain\Client\Client;
use Symfony\Component\Security\Core\User\UserInterface;

class KeycloakUser implements UserInterface
{
    /**
     * @var array<string, string>
     */
    private const array ROLE_MAP = [
        'full-access' => 'ROLE_FULL_ACCESS',
    ];

    /**
     * @var string[]
     */
    private array $roles = [];

    /**
     * @param string[] $roles
     */
    public function __construct(
        private readonly string $username,
        private readonly Client $client,
        array $roles,
    ) {
        foreach ($roles as $role) {
            if (isset(self::ROLE_MAP[$role])) {
                $this->roles[] = self::ROLE_MAP[$role];
            }
        }
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function eraseCredentials(): void
    {
    }

    public function getUserIdentifier(): string
    {
        assert('' !== $this->username);

        return $this->username;
    }

    public function getClient(): Client
    {
        return $this->client;
    }
}
