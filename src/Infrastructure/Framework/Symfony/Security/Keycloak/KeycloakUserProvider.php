<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Security\Keycloak;

use App\Domain\Client\Client;
use App\Domain\Client\Exception\UnrecognizedClientException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Core\User\AttributesBasedUserProviderInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @implements AttributesBasedUserProviderInterface<KeycloakUser>
 */
readonly class KeycloakUserProvider implements AttributesBasedUserProviderInterface
{
    public function __construct(
        private LoggerInterface $logger,
    ) {
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        assert($user instanceof KeycloakUser, 'User must be an instance of KeycloakUser');

        return $user;
    }

    public function supportsClass(string $class): bool
    {
        return KeycloakUser::class === $class;
    }

    /**
     * @param array<string, mixed> $attributes
     */
    public function loadUserByIdentifier(string $identifier, array $attributes = []): UserInterface
    {
        $this->logger->debug(
            'Keycloak user provider load user by identifier',
            ['identifier' => $identifier, 'attributes' => $attributes]
        );

        if (!isset($attributes['client_id']) || !is_string($attributes['client_id'])) {
            throw new UnauthorizedHttpException(
                'Cannot identify client from Keycloak. Client ID not found in attributes.'
            );
        }

        try {
            $client = new Client($attributes['client_id']);
        } catch (UnrecognizedClientException $exception) {
            throw new UnauthorizedHttpException($exception->getMessage());
        }

        $hermesIamRoles = [];

        if (
            isset($attributes['resource_access']['hermes-iam']['roles']) // @phpstan-ignore-line
            && is_array($attributes['resource_access']['hermes-iam']['roles'])
        ) {
            /** @phpstan-var string[] $hermesIamRoles */
            $hermesIamRoles = $attributes['resource_access']['hermes-iam']['roles'];
        }

        return new KeycloakUser(
            username: $identifier,
            client: $client,
            roles: $hermesIamRoles,
        );
    }
}
