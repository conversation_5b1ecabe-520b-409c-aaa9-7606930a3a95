<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Security;

use App\Domain\Client\Client;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

readonly class Security
{
    public function __construct(
        private \Symfony\Bundle\SecurityBundle\Security $symfonySecurity,
    ) {
    }

    public function getUser(): KeycloakUser
    {
        $user = $this->symfonySecurity->getUser();

        if (!$user instanceof KeycloakUser) {
            throw new AccessDeniedException('Invalid user.');
        }

        return $user;
    }

    /**
     * Get the current authenticated client.
     *
     * @throws AccessDeniedException if no user is authenticated or user is not a KeycloakUser
     */
    public function getCurrentClient(): Client
    {
        return $this->getUser()->getClient();
    }
}
