<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Validator;

use Symfony\Component\Validator\Constraints\Cascade;
use Symfony\Component\Validator\Mapping\ClassMetadata;
use Symfony\Component\Validator\Mapping\Loader\LoaderInterface;

readonly class AutoCascadeValidationLoader implements LoaderInterface
{
    public function loadClassMetadata(ClassMetadata $metadata): bool
    {
        $metadata->addConstraint(new Cascade());

        return true;
    }
}
