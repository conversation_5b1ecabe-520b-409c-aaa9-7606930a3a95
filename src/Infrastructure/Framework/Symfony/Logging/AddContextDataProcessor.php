<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Logging;

use App\Infrastructure\Framework\Context\RequestContextInterface;
use Monolog\Attribute\AsMonologProcessor;
use Monolog\LogRecord;

#[AsMonologProcessor]
readonly class AddContextDataProcessor
{
    public function __construct(
        private RequestContextInterface $requestContext,
    ) {
    }

    public function __invoke(LogRecord $logRecord): LogRecord
    {
        $logRecord->extra['request_id'] = $this->requestContext->getRequestId();
        $logRecord->extra['user'] = $this->requestContext->getAuthenticatedUserIdentifier();

        if (PHP_SAPI === 'cli') {
            /** @var string[] $args */
            $args = $_SERVER['argv'] ?? [];
            $command = implode(' ', $args);

            $logRecord->extra['command'] = $command;
        }

        return $logRecord;
    }
}
