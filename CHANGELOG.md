# Changelog

## [1.6.0](https://github.com/prezero/hermes-iam/compare/v1.5.1...v1.6.0) (2025-07-29)


### Features

* **HERMES-3917:** Ensure correct response format for error responses & validation ([#31](https://github.com/prezero/hermes-iam/issues/31)) ([0a1810d](https://github.com/prezero/hermes-iam/commit/0a1810daa4248f970400835c20e554f80e4dd702))

## [1.5.1](https://github.com/prezero/hermes-iam/compare/v1.5.0...v1.5.1) (2025-07-25)


### Bug Fixes

* security realm resolution ([fb4a838](https://github.com/prezero/hermes-iam/commit/fb4a838a5ad96b3e516d7804c229e50ca23edc50))

## [1.5.0](https://github.com/prezero/hermes-iam/compare/v1.4.0...v1.5.0) (2025-07-24)


### Features

* **HERMES-3902:** Adding context data to logs ([#28](https://github.com/prezero/hermes-iam/issues/28)) ([310fd35](https://github.com/prezero/hermes-iam/commit/310fd359058ce743168b06a7689a0ebb3eefd88f))

## [1.4.0](https://github.com/prezero/hermes-iam/compare/v1.3.0...v1.4.0) (2025-07-24)


### Features

* **HERMES-3894:** Password change endpoint ([#26](https://github.com/prezero/hermes-iam/issues/26)) ([05e488c](https://github.com/prezero/hermes-iam/commit/05e488c4fc4a1d513d0020318d40de5faabd93d4))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.31.1 ([#25](https://github.com/prezero/hermes-iam/issues/25)) ([ec528c4](https://github.com/prezero/hermes-iam/commit/ec528c4539e30e6f7dbb981290161829aa81bc11))
* **deps:** update update composer packages to non-major versions ([fd56fea](https://github.com/prezero/hermes-iam/commit/fd56feae8755bf095cf983510d8324c74dee343d))

## [1.3.0](https://github.com/prezero/hermes-iam/compare/v1.2.0...v1.3.0) (2025-07-22)


### Features

* **HERMES-3881:** Adding required actions on users create, improving existing endpoints and documentation ([#23](https://github.com/prezero/hermes-iam/issues/23)) ([dbad506](https://github.com/prezero/hermes-iam/commit/dbad506b4a9d250a01b4ffd84d5f44585048537f))

## [1.2.0](https://github.com/prezero/hermes-iam/compare/v1.1.0...v1.2.0) (2025-07-21)


### Features

* **HERMES-3816:** Endpoints for creating and updating user ([#22](https://github.com/prezero/hermes-iam/issues/22)) ([9a1c9af](https://github.com/prezero/hermes-iam/commit/9a1c9afbf10c153062b2a0b26527f3d7371b7430))
* **HERMES-3838:** User checking & retrievel endpoints ([#17](https://github.com/prezero/hermes-iam/issues/17)) ([2fab40f](https://github.com/prezero/hermes-iam/commit/2fab40f98400b5f36ed10b67df9ecc3c1e9679c2))
* **HERMES-3867:** Adding test URL as env var ([7368720](https://github.com/prezero/hermes-iam/commit/736872049fe2479103c5988330d51b7cf2e2cc19))
* **HERMES-3867:** IAM Tests on dev ([7fbf899](https://github.com/prezero/hermes-iam/commit/7fbf899e38672597a581bb3315c13a6e019e4538))
* **HERMES-3867:** IAM Tests on dev ([8235605](https://github.com/prezero/hermes-iam/commit/8235605d8adadb3f649b15f044445497bed882ee))
* **HERMES-3867:** Reading env vars from env ([57751c0](https://github.com/prezero/hermes-iam/commit/57751c0180d0509dc2bcddfdc873bb26b26844f2))
* **HERMES-3867:** Support ES256 algo from keycloak ([5e9e131](https://github.com/prezero/hermes-iam/commit/5e9e13141524167e9bfbefdc7fd9377308bbf5d1))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.3.0 ([#20](https://github.com/prezero/hermes-iam/issues/20)) ([520c0b8](https://github.com/prezero/hermes-iam/commit/520c0b838d0a11c982cc4a6b9e6e2b393c6a1625))
* **deps:** update prezero/workflows action to v1.28.0 ([#21](https://github.com/prezero/hermes-iam/issues/21)) ([e2fd92c](https://github.com/prezero/hermes-iam/commit/e2fd92c5b1e018f64c75c5429da37e130aabd76a))
* **deps:** update update local docker-compose env dependencies ([#19](https://github.com/prezero/hermes-iam/issues/19)) ([7c2c5f2](https://github.com/prezero/hermes-iam/commit/7c2c5f2fc8294419331a9a714a7824c84f9d399a))

## [1.1.0](https://github.com/prezero/hermes-iam/compare/v1.0.3...v1.1.0) (2025-07-10)


### Features

* **HERMES-3809:** Build dev image ([ad0d417](https://github.com/prezero/hermes-iam/commit/ad0d4170f87ebb52823b49f20db5723d1fb1a70a))
* **HERMES-3809:** Hermes IAM Deployment ([#16](https://github.com/prezero/hermes-iam/issues/16)) ([d40fcd5](https://github.com/prezero/hermes-iam/commit/d40fcd5a88d48164781660e42215cb9e144e61eb))


### Bug Fixes

* Deployment ([d74936f](https://github.com/prezero/hermes-iam/commit/d74936ff42641b9c43505c58e6199838cce4a052))
* Deployment ([40b23c5](https://github.com/prezero/hermes-iam/commit/40b23c5dd1f02f435a955cd1102979a19a8f975f))
* Removing blackfire extension for this project ([b35c5c9](https://github.com/prezero/hermes-iam/commit/b35c5c9f500dbd5a36ebc674ba7da5b91fc6372b))

## [1.0.3](https://github.com/prezero/hermes-iam/compare/v1.0.2...v1.0.3) (2025-07-10)


### Bug Fixes

* env vars ([4714c54](https://github.com/prezero/hermes-iam/commit/4714c549ea6f82b3a5e95cf06a84d697d642a135))

## [1.0.2](https://github.com/prezero/hermes-iam/compare/v1.0.1...v1.0.2) (2025-07-10)


### Bug Fixes

* missing dependencies for non-dev build ([760b8f2](https://github.com/prezero/hermes-iam/commit/760b8f210f01c071e8784b0586f80ae55349792c))

## [1.0.1](https://github.com/prezero/hermes-iam/compare/v1.0.0...v1.0.1) (2025-07-10)


### Bug Fixes

* docker directory is needed ([0080dd7](https://github.com/prezero/hermes-iam/commit/0080dd7840f83922e6128ca756559dc6d5c5f03c))

## [1.0.0](https://github.com/prezero/hermes-iam/compare/v0.1.0...v1.0.0) (2025-07-10)


### ⚠ BREAKING CHANGES

* **HERMES-3794:** Bootstrap Symfony API project ([#3](https://github.com/prezero/hermes-iam/issues/3))

### Features

* **HERMES-3772:** Extract keycloak docker local setup ([d96b336](https://github.com/prezero/hermes-iam/commit/d96b3360678e1c90b267eccbccbe41c9a3e9d747))
* **HERMES-3782:** Adding configuration as a code for Keycloak, instead of dump ([#1](https://github.com/prezero/hermes-iam/issues/1)) ([5b735b2](https://github.com/prezero/hermes-iam/commit/5b735b2050323e97a696eab68327bb43a05f793d))
* **HERMES-3792:** Add user groups in JWT Access token ([#2](https://github.com/prezero/hermes-iam/issues/2)) ([2f024ba](https://github.com/prezero/hermes-iam/commit/2f024bafc5d3f29a3ce6738eff99230946cfa0fa))
* **HERMES-3794:** Bootstrap Symfony API project ([#3](https://github.com/prezero/hermes-iam/issues/3)) ([bb5b740](https://github.com/prezero/hermes-iam/commit/bb5b7400085f9ede8db9bd933d49fff778c7ae05))
* **HERMES-3803:** Adding authentication via keycloak ([#5](https://github.com/prezero/hermes-iam/issues/5)) ([9f28d05](https://github.com/prezero/hermes-iam/commit/9f28d059069832c9d1a7fec376f15882a3a8fc7c))


### Miscellaneous Chores

* **deps:** pin adorsys/keycloak-config-cli docker tag to 44fcaca ([#6](https://github.com/prezero/hermes-iam/issues/6)) ([ddaeeb6](https://github.com/prezero/hermes-iam/commit/ddaeeb6959fb9aa3c93a7ddb7c99570fc5ff2b33))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.2.2 ([#8](https://github.com/prezero/hermes-iam/issues/8)) ([b82595f](https://github.com/prezero/hermes-iam/commit/b82595f94ebded6b06bf2b0bcf62db0b1575757e))
* **deps:** update prezero/workflows action to v1.27.0 ([#9](https://github.com/prezero/hermes-iam/issues/9)) ([a3b97fc](https://github.com/prezero/hermes-iam/commit/a3b97fcb6e735d6026a110964f3eec5fe978f239))
